import clang.cindex
import json
import os
import sys

# 设置 libclang 库的路径，根据你的环境可能需要修改
# clang.cindex.Config.set_library_file('/usr/lib/x86_64-linux-gnu/libclang-14.so')

def traverse_ast(cursor, collected_data):
    """
    递归遍历AST，提取信息
    """
    # 我们只关心当前文件中的定义，忽略从 #include 引入的
    if cursor.location.file and cursor.location.file.name != sys.argv[1]:
        return

    # 提取函数声明/定义
    if cursor.kind == clang.cindex.CursorKind.FUNCTION_DECL:
        func_name = cursor.spelling
        func_type = cursor.type.spelling
        collected_data['functions'][func_name] = func_type
        # print(f"Found Function: {func_name} with type {func_type}")

    # 提取结构体声明
    elif cursor.kind == clang.cindex.CursorKind.STRUCT_DECL:
        struct_name = cursor.spelling
        if struct_name: # 忽略匿名的 struct
            members = [field.spelling for field in cursor.get_children() if field.kind == clang.cindex.CursorKind.FIELD_DECL]
            collected_data['structs'][struct_name] = members
            # print(f"Found Struct: {struct_name} with members {members}")

    # 递归遍历子节点
    for child in cursor.get_children():
        traverse_ast(child, collected_data)

def parse_file(file_path, compilation_database_path):
    """
    解析单个文件并返回结构化的数据
    """
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}", file=sys.stderr)
        return None
        
    try:
        compdb = clang.cindex.CompilationDatabase.fromDirectory(compilation_database_path)
        # 获取该文件的编译命令
        commands = compdb.getCompileCommands(file_path)
        if not commands:
            print(f"No compile commands found for {file_path}", file=sys.stderr)
            return None
        
        # 使用编译命令中的参数进行解析
        args = list(commands[0].arguments)[1:] # [1:] a-la gcc/clang, to skip the executable path
        
        index = clang.cindex.Index.create()
        # tu = Translation Unit
        tu = index.parse(file_path, args=args)
        
        if not tu:
            print(f"Unable to parse {file_path}", file=sys.stderr)
            return None
            
        # 检查诊断信息（编译错误/警告）
        has_errors = False
        for diag in tu.diagnostics:
            if diag.severity >= clang.cindex.Diagnostic.Error:
                print(f"Error parsing {file_path}: {diag.spelling}", file=sys.stderr)
                has_errors = True
        
        if has_errors:
            return None

        collected_data = {
            'functions': {},
            'structs': {},
            # 你可以添加更多类型，如 'globals', 'macros', 'enums' 等
        }
        traverse_ast(tu.cursor, collected_data)
        return collected_data

    except Exception as e:
        print(f"An error occurred while parsing {file_path}: {e}", file=sys.stderr)
        return None

def compare_data(data1, data2):
    """
    比较从两份代码中提取出的数据
    """
    # 比较函数
    funcs1 = set(data1['functions'].keys())
    funcs2 = set(data2['functions'].keys())
    
    print("\n--- Function Comparison ---")
    print(f"Only in file 1: {sorted(list(funcs1 - funcs2))}")
    print(f"Only in file 2: {sorted(list(funcs2 - funcs1))}")
    
    common_funcs = funcs1.intersection(funcs2)
    changed_funcs = []
    for func in common_funcs:
        if data1['functions'][func] != data2['functions'][func]:
            changed_funcs.append(f"{func} (Signature changed)")
            # print(f"  - {func}:")
            # print(f"    - File 1: {data1['functions'][func]}")
            # print(f"    - File 2: {data2['functions'][func]}")
    print(f"Functions with changed signature: {changed_funcs}")

    # 比较结构体
    structs1 = set(data1['structs'].keys())
    structs2 = set(data2['structs'].keys())

    print("\n--- Struct Comparison ---")
    print(f"Only in file 1: {sorted(list(structs1 - structs2))}")
    print(f"Only in file 2: {sorted(list(structs2 - structs1))}")
    
    common_structs = structs1.intersection(structs2)
    changed_structs = []
    for struct in common_structs:
        if set(data1['structs'][struct]) != set(data2['structs'][struct]):
             changed_structs.append(f"{struct} (Members changed)")
    print(f"Structs with changed members: {changed_structs}")


if __name__ == "__main__":
    # --- 配置区 ---
    # 假设这是两套代码中一个相对路径相同的文件
    relative_file_path = "device/board/hihope/rk3568/peripheral/hdf_config.c" # 请替换为实际要对比的文件

    # rk3568 的配置
    code_path_rk3568 = "/path/to/oh_rk3568"
    db_path_rk3568 = os.path.join(code_path_rk3568, "out/rk3568") # 编译数据库路径
    file_path_rk3568 = os.path.join(code_path_rk3568, relative_file_path)

    # dayu210 的配置 (以 Hi3516DV300 为例)
    code_path_dayu210 = "/path/to/oh_dayu210"
    db_path_dayu210 = os.path.join(code_path_dayu210, "out/dayu210") # 编译数据库路径
    # 注意：dayu210 的 device 目录结构可能不同，这里仅作示例
    # 你需要找到 dayu210 中功能对等的文件
    relative_file_path_dayu = "device/hisilicon/hispark_taurus/sdk_liteos/hdf_config.c" # 假设这是 dayu210 中对等的文件
    file_path_dayu210 = os.path.join(code_path_dayu210, relative_file_path_dayu)
    
    # --- 执行解析和对比 ---
    print(f"Parsing file 1: {file_path_rk3568}")
    data_rk3568 = parse_file(file_path_rk3568, db_path_rk3568)

    print(f"\nParsing file 2: {file_path_dayu210}")
    data_dayu210 = parse_file(file_path_dayu210, db_path_dayu210)

    if data_rk3568 and data_dayu210:
        print("\n\n=== Comparison Report ===")
        print(f"File 1: {file_path_rk3568}")
        print(f"File 2: {file_path_dayu210}")
        compare_data(data_rk3568, data_dayu210)