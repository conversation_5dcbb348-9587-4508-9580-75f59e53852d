import clang.cindex
import json
import os
import shlex
import re
from collections import defaultdict, deque

# --- 全局配置 ---
PROJECT_ROOT = '/home/<USER>/openharmony-5.0.0'
COMPARE_FILE = 'device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_face_node.cpp'

BOARDS_INFO = {
    "rk3568": {
        "host_db_path": 'compile_commands.rk3568.filtered.json',
        "board_path_prefix": "device/board/hihope/rk3568",
        "target_prefixes": [
            "device/board/hihope/rk3568/",
            "vendor/hihope/",
            "device/soc/rockchip/rk3568/",
        ]
    }
}

def load_and_filter_db(host_db_path, target_prefixes):
    """加载并过滤编译数据库，使用绝对路径作为字典的键"""
    print(f"正在从 {host_db_path} 加载并过滤数据...")
    try:
        with open(host_db_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {host_db_path}")
        return None

    filtered_commands = {}
    print("开始筛选...")
    for command in data:
        relative_file_path = command.get('file')
        compile_directory = command.get('directory')

        if not relative_file_path or not compile_directory:
            continue
        
        # 构建绝对路径
        absolute_file_path = os.path.abspath(os.path.join(compile_directory, relative_file_path))

        # 使用绝对路径进行过滤
        if any(prefix in absolute_file_path for prefix in target_prefixes):
            filtered_commands[absolute_file_path] = command
            
    print(f"筛选完成，找到 {len(filtered_commands)} 条相关命令")
    return filtered_commands

def analyze_function_calls(file_path):
    """分析文件中的函数调用关系"""
    function_calls = defaultdict(set)
    function_definitions = set()

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # 简单的函数调用模式匹配（可能不够精确，但足够演示）
        # 匹配函数定义 - 改进的正则表达式
        func_def_patterns = [
            r'^\s*(?:static\s+)?(?:inline\s+)?(?:virtual\s+)?(?:\w+\s+)*?(\w+)\s*\([^)]*\)\s*\{',  # 普通函数
            r'^\s*(?:\w+\s*::\s*)?(\w+)\s*\([^)]*\)\s*\{',  # 类方法
            r'^\s*(?:static\s+)?(?:inline\s+)?(?:const\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*\{'  # 带返回类型的函数
        ]

        for pattern in func_def_patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            function_definitions.update(matches)

        # 过滤掉一些常见的非函数名
        common_keywords = {'if', 'for', 'while', 'switch', 'return', 'sizeof', 'typeof', 'static_cast', 'dynamic_cast', 'const_cast', 'reinterpret_cast'}
        function_definitions = {f for f in function_definitions if f not in common_keywords and len(f) > 1}

        # 匹配函数调用
        func_call_pattern = r'(\w+)\s*\('
        all_calls = re.findall(func_call_pattern, content)

        # 过滤调用列表
        filtered_calls = [call for call in all_calls if call not in common_keywords and len(call) > 1]

        # 为了简化，我们假设每个函数都可能调用文件中出现的其他函数
        # 实际应该通过AST分析来获得更准确的调用关系
        for func in function_definitions:
            for call in filtered_calls:
                if call != func and call in function_definitions:
                    function_calls[func].add(call)

    except Exception as e:
        print(f"分析函数调用时出错: {e}")
        import traceback
        traceback.print_exc()

    return function_calls, function_definitions

def parse_file_for_calls(file_path, command_entry):
    """解析文件并提取函数调用信息"""
    if not command_entry:
        print(f"警告: 找不到文件 {file_path} 的编译命令")
        return None

    original_cwd = os.getcwd()
    try:
        working_dir = command_entry['directory']
        args = shlex.split(command_entry['command'])
        
        print(f"  - 工作目录: {working_dir}")
        os.chdir(working_dir)

        if 'ccache' in os.path.basename(args[0]):
            args = args[1:]
        
        args_for_clang = args[1:]
        
        # 创建简化的参数列表
        simple_args = []
        for arg in args_for_clang:
            if arg.startswith('-I'):
                simple_args.append(arg)
            elif arg.startswith('-D') and not any(skip in arg for skip in ['__DATE__', '__TIME__', '__TIMESTAMP__']):
                simple_args.append(arg)
            elif arg in ['-std=c++17', '-std=c++14', '-std=c++11']:
                simple_args.append(arg)
        
        simple_args.extend([
            '-std=c++17',
            '-x', 'c++',
            '-w',  # 禁用所有警告
            '-Wno-error',  # 将错误降级为警告
            '-fsyntax-only',  # 只进行语法检查
            '-I/usr/include',
            '-isystem', '/usr/include/c++/9',
            '-isystem', '/usr/include/x86_64-linux-gnu/c++/9',
        ])
        
        print(f"  - 简化参数数量: {len(simple_args)}")

        if not os.path.exists(file_path):
            print(f"  - 错误: 文件不存在 {file_path}")
            return None

        print(f"  - 开始AST解析...")

        index = clang.cindex.Index.create()
        parse_options = (
            clang.cindex.TranslationUnit.PARSE_SKIP_FUNCTION_BODIES |
            clang.cindex.TranslationUnit.PARSE_INCOMPLETE
        )
        
        tu = index.parse(file_path, args=simple_args, options=parse_options)

        if not tu:
            print(f"  - 错误: 无法创建翻译单元")
            return None

        print(f"  - 翻译单元创建成功")

        # 提取函数信息和调用关系
        functions = {}
        call_graph = defaultdict(set)
        
        traverse_for_calls(tu.cursor, functions, call_graph, file_path)
        
        # 同时使用简单的文本分析作为补充
        text_calls, text_funcs = analyze_function_calls(file_path)
        
        print(f"  - 成功提取: {len(functions)} 个函数定义")
        print(f"  - 文本分析发现: {len(text_funcs)} 个函数")
        
        return {
            'functions': functions,
            'call_graph': dict(call_graph),
            'text_calls': dict(text_calls),
            'all_functions': text_funcs
        }
        
    except Exception as e:
        print(f"处理文件 {file_path} 时发生异常: {e}")
        return None
    finally:
        os.chdir(original_cwd)

def traverse_for_calls(cursor, functions, call_graph, source_file_path):
    """遍历AST提取函数定义和调用关系"""
    try:
        if cursor.location.file and cursor.location.file.name == source_file_path:
            # 函数定义
            if cursor.kind in [clang.cindex.CursorKind.FUNCTION_DECL, clang.cindex.CursorKind.CXX_METHOD]:
                if cursor.spelling and cursor.is_definition():
                    functions[cursor.spelling] = {
                        'line': cursor.location.line,
                        'type': cursor.type.spelling,
                        'calls': []
                    }

                    # 查找函数体中的调用
                    current_func = cursor.spelling
                    extract_function_calls(cursor, current_func, call_graph, functions)

        # 递归遍历子节点
        for child in cursor.get_children():
            traverse_for_calls(child, functions, call_graph, source_file_path)

    except Exception as e:
        print(f"AST遍历时出错: {e}")

def extract_function_calls(func_cursor, func_name, call_graph, functions):
    """从函数体中提取函数调用"""
    try:
        for child in func_cursor.walk_preorder():
            if child.kind == clang.cindex.CursorKind.CALL_EXPR:
                # 获取被调用的函数名
                called_func = None

                # 尝试从不同的子节点获取函数名
                for call_child in child.get_children():
                    if call_child.kind == clang.cindex.CursorKind.DECL_REF_EXPR:
                        called_func = call_child.spelling
                        break
                    elif call_child.kind == clang.cindex.CursorKind.MEMBER_REF_EXPR:
                        called_func = call_child.spelling
                        break
                    elif call_child.kind == clang.cindex.CursorKind.UNEXPOSED_EXPR:
                        # 对于一些复杂的调用表达式
                        for nested_child in call_child.get_children():
                            if nested_child.spelling:
                                called_func = nested_child.spelling
                                break

                # 如果找到了被调用的函数名
                if called_func and called_func != func_name:
                    call_graph[func_name].add(called_func)
                    if func_name in functions:
                        functions[func_name]['calls'].append(called_func)

    except Exception as e:
        print(f"提取函数调用时出错: {e}")

def analyze_call_statistics(call_data):
    """分析调用统计信息"""
    functions = call_data.get('functions', {})
    call_graph = call_data.get('call_graph', {})
    text_calls = call_data.get('text_calls', {})

    # 合并调用关系
    merged_calls = defaultdict(set)
    for func, calls in call_graph.items():
        merged_calls[func].update(calls)
    for func, calls in text_calls.items():
        merged_calls[func].update(calls)

    # 计算被调用次数
    call_count = defaultdict(int)
    for caller, callees in merged_calls.items():
        for callee in callees:
            call_count[callee] += 1

    # 找出最常被调用的函数
    most_called = sorted(call_count.items(), key=lambda x: x[1], reverse=True)[:5]

    # 找出调用最多其他函数的函数
    most_calling = sorted(merged_calls.items(), key=lambda x: len(x[1]), reverse=True)[:5]

    return {
        'call_count': dict(call_count),
        'most_called': most_called,
        'most_calling': most_calling,
        'merged_calls': dict(merged_calls)
    }

def print_call_statistics(stats):
    """打印调用统计信息"""
    print("\n" + "="*60)
    print("                    调用统计分析")
    print("="*60)

    print("\n🔥 最常被调用的函数 (热点函数):")
    for i, (func, count) in enumerate(stats['most_called'], 1):
        print(f"   {i}. {func} - 被调用 {count} 次")

    print("\n📞 调用最多其他函数的函数 (复杂函数):")
    for i, (func, callees) in enumerate(stats['most_calling'], 1):
        print(f"   {i}. {func} - 调用了 {len(callees)} 个其他函数")
        if len(callees) <= 5:
            print(f"      调用: {', '.join(sorted(callees))}")
        else:
            print(f"      调用: {', '.join(sorted(list(callees)[:5]))} ... 等{len(callees)}个")

def print_call_tree(call_data, max_depth=5):
    """打印函数调用树形结构"""
    if not call_data:
        print("没有找到调用数据")
        return

    functions = call_data.get('functions', {})
    call_graph = call_data.get('call_graph', {})
    text_calls = call_data.get('text_calls', {})

    # 合并AST和文本分析的结果
    merged_calls = defaultdict(set)
    for func, calls in call_graph.items():
        merged_calls[func].update(calls)
    for func, calls in text_calls.items():
        merged_calls[func].update(calls)

    print("\n" + "="*60)
    print("                    函数调用树形结构")
    print("="*60)

    # 找到根函数（被调用最少的函数）
    all_funcs = set(functions.keys()) | set(merged_calls.keys())
    called_funcs = set()
    for calls in merged_calls.values():
        called_funcs.update(calls)

    root_funcs = all_funcs - called_funcs
    if not root_funcs:
        root_funcs = list(all_funcs)[:3]  # 如果没有明显的根函数，取前几个

    print(f"\n发现 {len(all_funcs)} 个函数，{len(root_funcs)} 个根函数")
    print(f"根函数: {', '.join(list(root_funcs)[:5])}")

    # 为每个根函数打印调用树
    for root_func in list(root_funcs)[:3]:  # 限制显示前3个根函数
        print(f"\n📁 调用树 - 根函数: {root_func}")
        if root_func in functions:
            print(f"   位置: 第 {functions[root_func]['line']} 行")
            print(f"   类型: {functions[root_func].get('type', '未知')}")
        print("   " + "-" * 50)

        visited = set()
        print_tree_recursive(root_func, merged_calls, functions, 0, max_depth, visited)

    # 打印调用统计
    stats = analyze_call_statistics(call_data)
    print_call_statistics(stats)

def detect_cycles(call_graph):
    """检测调用图中的循环"""
    cycles = []
    visited = set()
    rec_stack = set()

    def dfs(node, path):
        if node in rec_stack:
            # 找到循环
            cycle_start = path.index(node)
            cycle = path[cycle_start:] + [node]
            cycles.append(cycle)
            return

        if node in visited:
            return

        visited.add(node)
        rec_stack.add(node)
        path.append(node)

        for neighbor in call_graph.get(node, []):
            dfs(neighbor, path)

        rec_stack.remove(node)
        path.pop()

    for node in call_graph:
        if node not in visited:
            dfs(node, [])

    return cycles

def calculate_call_depth(call_graph, start_func):
    """计算从指定函数开始的最大调用深度"""
    max_depth = 0
    visited = set()

    def dfs(func, depth):
        nonlocal max_depth
        if func in visited or depth > 20:  # 防止无限递归
            return

        visited.add(func)
        max_depth = max(max_depth, depth)

        for called_func in call_graph.get(func, []):
            dfs(called_func, depth + 1)

        visited.remove(func)

    dfs(start_func, 0)
    return max_depth

def print_cycle_analysis(call_data):
    """打印循环调用分析"""
    merged_calls = defaultdict(set)
    call_graph = call_data.get('call_graph', {})
    text_calls = call_data.get('text_calls', {})

    for func, calls in call_graph.items():
        merged_calls[func].update(calls)
    for func, calls in text_calls.items():
        merged_calls[func].update(calls)

    cycles = detect_cycles(dict(merged_calls))

    print("\n" + "="*60)
    print("                    循环调用检测")
    print("="*60)

    if cycles:
        print(f"\n⚠️  发现 {len(cycles)} 个潜在的循环调用:")
        for i, cycle in enumerate(cycles, 1):
            print(f"\n   循环 {i}: {' -> '.join(cycle)}")
    else:
        print("\n✅ 未发现循环调用")

def print_tree_recursive(func_name, call_graph, functions, depth, max_depth, visited):
    """递归打印调用树"""
    if depth > max_depth:
        print("  " * depth + f"└── ... (达到最大深度 {max_depth})")
        return

    if func_name in visited:
        print("  " * depth + f"├── {func_name} (🔄 循环调用)")
        return

    visited.add(func_name)

    # 打印当前函数
    indent = "  " * depth
    if depth == 0:
        symbol = "🌳"
    else:
        symbol = "├──"

    line_info = ""
    type_info = ""
    if func_name in functions:
        line_info = f" (第{functions[func_name]['line']}行)"
        func_type = functions[func_name].get('type', '')
        if 'static' in func_type.lower():
            type_info = " [静态]"

    print(f"{indent}{symbol} {func_name}{line_info}{type_info}")

    # 打印调用的函数
    if func_name in call_graph:
        calls = sorted(call_graph[func_name])
        for i, called_func in enumerate(calls):
            if i < 8:  # 限制每个函数最多显示8个调用
                print_tree_recursive(called_func, call_graph, functions, depth + 1, max_depth, visited.copy())
            elif i == 8:
                print("  " * (depth + 1) + f"└── ... 还有 {len(calls) - 8} 个调用")
                break

def print_depth_analysis(call_data):
    """打印调用深度分析"""
    functions = call_data.get('functions', {})
    merged_calls = defaultdict(set)
    call_graph = call_data.get('call_graph', {})
    text_calls = call_data.get('text_calls', {})

    for func, calls in call_graph.items():
        merged_calls[func].update(calls)
    for func, calls in text_calls.items():
        merged_calls[func].update(calls)

    print("\n" + "="*60)
    print("                    调用深度分析")
    print("="*60)

    # 计算每个函数的最大调用深度
    depth_info = []
    for func in functions:
        depth = calculate_call_depth(dict(merged_calls), func)
        depth_info.append((func, depth))

    # 按深度排序
    depth_info.sort(key=lambda x: x[1], reverse=True)

    print(f"\n📏 函数调用深度排行 (前10名):")
    for i, (func, depth) in enumerate(depth_info[:10], 1):
        line_info = f"第{functions[func]['line']}行" if func in functions else "未知位置"
        print(f"   {i:2d}. {func} - 最大深度: {depth} ({line_info})")

def main():
    board_name = "rk3568"

    db = load_and_filter_db(BOARDS_INFO[board_name]['host_db_path'], BOARDS_INFO[board_name]['target_prefixes'])

    if not db:
        print("数据库加载失败，无法继续")
        return

    file_to_analyze = os.path.join(PROJECT_ROOT, COMPARE_FILE)

    print("\n" + "="*60)
    print("                 C++ 文件调用链分析工具")
    print("="*60)
    print(f"📁 分析文件: {file_to_analyze}")
    print(f"🔧 目标板子: {board_name}")

    print(f"\n🔍 开始分析 {board_name} 板子的文件...")
    call_data = parse_file_for_calls(file_to_analyze, db.get(file_to_analyze))

    if call_data:
        # 1. 打印调用树
        print_call_tree(call_data)

        # 2. 打印循环检测
        print_cycle_analysis(call_data)

        # 3. 打印深度分析
        print_depth_analysis(call_data)

        # 4. 打印最终统计信息
        functions = call_data.get('functions', {})
        call_graph = call_data.get('call_graph', {})
        text_calls = call_data.get('text_calls', {})

        print("\n" + "="*60)
        print("                    最终统计报告")
        print("="*60)

        print(f"\n📊 基础统计:")
        print(f"   - 函数定义总数: {len(functions)}")
        print(f"   - AST发现的调用关系: {len(call_graph)}")
        print(f"   - 文本分析发现的调用关系: {len(text_calls)}")

        total_ast_calls = sum(len(calls) for calls in call_graph.values())
        total_text_calls = sum(len(calls) for calls in text_calls.values())
        print(f"   - AST总调用次数: {total_ast_calls}")
        print(f"   - 文本分析总调用次数: {total_text_calls}")

        if functions:
            avg_calls = total_ast_calls / len(call_graph) if call_graph else 0
            print(f"   - 平均每函数调用数: {avg_calls:.1f}")

        print(f"\n✅ 分析完成！")

    else:
        print("\n❌ 未能成功解析文件，无法生成调用树")
        print("💡 可能的原因:")
        print("   - 编译命令不正确")
        print("   - 缺少必要的头文件")
        print("   - 文件路径错误")
        print("   - clang库版本不兼容")

if __name__ == '__main__':
    try:
        import clang.cindex
        main()
    except ImportError:
        print("错误: 找不到 clang python 库。请运行 'python3 -m pip install libclang'")
        exit()
