import clang.cindex
import json
import os
import shlex

# --- Global Configuration ---
PROJECT_ROOT = '/home/<USER>/openharmony-5.0.0'

BOARDS_INFO = {
    "rk3568": {
        "host_db_path": 'compile_commands.rk3568.filtered.json',
        "board_path_prefix": "device/board/hihope/rk3568",
        "target_prefixes": [
            "device/board/hihope/rk3568/",
            "vendor/hihope/",
            "device/soc/rockchip/rk3568/",
        ]
    },
    "orangepi_5b": {
        "host_db_path":  'compile_commands.orangepi_5b.filtered.json',
        "board_path_prefix": "device/board/orangepi/orangepi_5b",
        "target_prefixes": [
            "device/board/orangepi/orangepi_5b/",
            "vendor/orangepi/",
            "device/soc/rockchip/rk3588s/",
        ]
    }
}

def load_and_filter_db(host_db_path, target_prefixes):
    """Load and filter compilation database, using absolute paths as dictionary keys."""
    print(f"Loading and filtering data from {host_db_path}...")
    try:
        with open(host_db_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: Cannot find file {host_db_path}.")
        return None

    filtered_commands = {}
    print("Starting filtering...")
    for command in data:
        relative_file_path = command.get('file')
        compile_directory = command.get('directory')

        if not relative_file_path or not compile_directory:
            continue

        # Build absolute path
        absolute_file_path = os.path.abspath(os.path.join(compile_directory, relative_file_path))

        # Filter using absolute path
        if any(prefix in absolute_file_path for prefix in target_prefixes):
            filtered_commands[absolute_file_path] = command

    print(f"Filtering completed, found {len(filtered_commands)} related commands.")
    return filtered_commands

def parse_file_simple(file_path, command_entry):
    """Simplified file parsing function"""
    if not command_entry:
        print(f"Warning: Cannot find compilation command for file {file_path}.")
        return None

    original_cwd = os.getcwd()
    try:
        working_dir = command_entry['directory']
        args = shlex.split(command_entry['command'])

        print(f"  - Working directory: {working_dir}")
        os.chdir(working_dir)

        if 'ccache' in os.path.basename(args[0]):
            args = args[1:]

        args_for_clang = args[1:]

        # Create a minimized argument list
        simple_args = []

        # Only keep header file paths and basic definitions
        for arg in args_for_clang:
            if arg.startswith('-I'):
                simple_args.append(arg)
            elif arg.startswith('-D') and not any(skip in arg for skip in ['__DATE__', '__TIME__', '__TIMESTAMP__', '__has_builtin', '__has_feature', '__has_extension', '__has_attribute', '__has_cpp_attribute']):
                simple_args.append(arg)
            elif arg in ['-std=c++17', '-std=c++14', '-std=c++11']:
                simple_args.append(arg)

        # Add necessary system macro definitions (avoid redefining built-in macros)
        system_defines = [
            '-D__GLIBC_PREREQ(maj,min)=1',
            '-D__GLIBC_USE(feature)=1',
            '-D__GNUC_PREREQ(maj,min)=1',
            '-D_GNU_SOURCE=1',
            '-D__STDC_HOSTED__=1',
            '-Drestrict=__restrict__',  # Resolve restrict keyword conflict
            '-D__restrict=__restrict__',
            '-D_Pragma(x)=',  # Ignore pragma directives
        ]

        # Add basic C++ standard and system definitions
        simple_args.extend([
            '-std=c++17',
            '-x', 'c++',
            '-w',  # Disable all warnings
            '-Wno-error',  # Downgrade errors to warnings
            '-fsyntax-only',  # Only perform syntax checking
            '-fno-builtin',  # Disable built-in functions
        ])

        # Add system macro definitions
        simple_args.extend(system_defines)

        # Simplify header file path handling, avoid system path conflicts
        # Use alternative paths, but exclude potentially conflicting system paths
        simple_args.extend([
            '-I/usr/include',
            '-isystem', '/usr/include/c++/9',
            '-isystem', '/usr/include/x86_64-linux-gnu/c++/9',
        ])

        print(f"  - Simplified argument count: {len(simple_args)}")
        print(f"  - System macro definition count: {len(system_defines)}")

        # Check if file exists
        if not os.path.exists(file_path):
            print(f"  - Error: File does not exist {file_path}")
            return None

        print(f"  - Starting parsing...")

        index = clang.cindex.Index.create()

        # Use the most permissive parsing options
        parse_options = (
            clang.cindex.TranslationUnit.PARSE_DETAILED_PROCESSING_RECORD |
            clang.cindex.TranslationUnit.PARSE_INCOMPLETE
        )

        tu = index.parse(file_path, args=simple_args, options=parse_options)

        if not tu:
            print(f"  - Error: Cannot create translation unit")
            return None

        print(f"  - Translation unit created successfully")

        # Use enhanced diagnostic information display
        error_count, warning_count = enhanced_diagnostics(tu)

        if error_count > 0:
            print(f"  - Found {error_count} errors, but continuing to extract AST...")

        data_store = {'functions': {}, 'structs': {}, 'classes': {}}
        traverse_ast(tu.cursor, data_store, file_path)
        print(f"  - Successfully extracted: {len(data_store['functions'])} functions, {len(data_store['structs'])} structs.")
        return data_store

    except Exception as e:
        print(f"Exception occurred while processing file {file_path}")
        return None
    finally:
        os.chdir(original_cwd)

def enhanced_diagnostics(tu):
    """Enhanced diagnostic information display"""
    error_count = 0
    warning_count = 0

    # print("  - Detailed diagnostic information:")
    # for diag in tu.diagnostics:
    #     severity_name = {
    #         0: "Ignored",    # Diagnostic.Ignored
    #         1: "Note",       # Diagnostic.Note
    #         2: "Warning",    # Diagnostic.Warning
    #         3: "Error",      # Diagnostic.Error
    #         4: "Fatal"       # Diagnostic.Fatal
    #     }.get(diag.severity, "Unknown")

    #     location = f"{diag.location.file.name if diag.location.file else 'Unknown file'}:{diag.location.line}:{diag.location.column}"

    #     if diag.severity >= 3:  # Error level
    #         error_count += 1
    #         # Remove specific error messages, only show generic error info
    #         print(f"    [{severity_name}] {location}: Compilation error detected")
    #     elif diag.severity == 2:  # Warning level
    #         warning_count += 1
    #         if warning_count <= 5:  # Only show first 5 warnings
    #             print(f"    [{severity_name}] {location}: Compilation warning detected")

    # if warning_count > 5:
    #     print(f"    ... {warning_count - 5} more warnings not displayed")

    print(f"  - Diagnostic summary: {error_count} errors, {warning_count} warnings")

    return error_count, warning_count

def traverse_ast(cursor, data_store, source_file_full_path):
    """Traverse AST and extract information"""
    try:
        if cursor.location.file and cursor.location.file.name == source_file_full_path:
            if cursor.kind in [clang.cindex.CursorKind.FUNCTION_DECL, clang.cindex.CursorKind.CXX_METHOD]:
                if cursor.spelling:  # Ensure function name is not empty
                    data_store['functions'][cursor.spelling] = cursor.type.spelling
            elif cursor.kind == clang.cindex.CursorKind.STRUCT_DECL and cursor.is_definition():
                if cursor.spelling:
                    fields = [f.spelling for f in cursor.get_children() if f.kind == clang.cindex.CursorKind.FIELD_DECL]
                    data_store['structs'][cursor.spelling] = sorted(fields)

        # Recursively traverse child nodes
        for child in cursor.get_children():
            traverse_ast(child, data_store, source_file_full_path)
    except Exception:
        # Ignore errors during traversal, continue processing other nodes
        pass

def compare_ast_data(data1, data2, name1, name2):
    print("\n" + "="*20 + " Code Structure Difference Report " + "="*20)
    funcs1, funcs2 = set(data1['functions']), set(data2['functions'])
    print("--- Function Comparison ---")
    for f in sorted(funcs1 - funcs2):
        print(f"(+) {name1} unique function: {f}")
    for f in sorted(funcs2 - funcs1):
        print(f"(-) {name2} unique function: {f}")
    for f in sorted(funcs1 & funcs2):
        if data1['functions'][f] != data2['functions'][f]:
            print(f"(*) Function signature changed: {f}")
            print(f"      {name1}: {data1['functions'][f]}")
            print(f"      {name2}: {data2['functions'][f]}")

    structs1, structs2 = set(data1['structs']), set(data2['structs'])
    print("\n--- Struct Comparison ---")
    for s in sorted(structs1 - structs2):
        print(f"(+) {name1} unique struct: {s}")
    for s in sorted(structs2 - structs1):
        print(f"(-) {name2} unique struct: {s}")
    for s in sorted(structs1 & structs2):
        if data1['structs'][s] != data2['structs'][s]:
            print(f"(*) Struct changed: {s}")
            print(f"      {name1}: {data1['structs'][s]}")
            print(f"      {name2}: {data2['structs'][s]}")
    print("-" * 50)

def main():
    board1_name = "rk3568"
    board2_name = "orangepi_5b"

    db1 = load_and_filter_db(BOARDS_INFO[board1_name]['host_db_path'], BOARDS_INFO[board1_name]['target_prefixes'])
    db2 = load_and_filter_db(BOARDS_INFO[board2_name]['host_db_path'], BOARDS_INFO[board2_name]['target_prefixes'])

    if not db1 or not db2:
        print("Database loading failed, cannot continue.")
        return

    file_to_compare_b1 = os.path.join(PROJECT_ROOT, 'device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_codec_node.cpp')

    prefix1 = BOARDS_INFO[board1_name]['board_path_prefix']
    prefix2 = BOARDS_INFO[board2_name]['board_path_prefix']
    file_to_compare_b2 = file_to_compare_b1.replace(prefix1, prefix2)

    print("\n" + "="*20 + " Starting File Comparison " + "="*20)
    print(f"File to compare 1: {file_to_compare_b1}")
    print(f"File to compare 2: {file_to_compare_b2}")

    print(f"\nAnalyzing {board1_name}...")
    info1 = parse_file_simple(file_to_compare_b1, db1.get(file_to_compare_b1))

    print(f"\nAnalyzing {board2_name}...")
    info2 = parse_file_simple(file_to_compare_b2, db2.get(file_to_compare_b2))

    if info1 and info2:
        compare_ast_data(info1, info2, board1_name, board2_name)
    elif info1:
        print(f"\nOnly successfully parsed {board1_name} file, cannot compare.")
        print(f"{board1_name} contains {len(info1['functions'])} functions, {len(info1['structs'])} structs.")
    elif info2:
        print(f"\nOnly successfully parsed {board2_name} file, cannot compare.")
        print(f"{board2_name} contains {len(info2['functions'])} functions, {len(info2['structs'])} structs.")
    else:
        print("\nFailed to parse any files, cannot compare.")


if __name__ == '__main__':
    try:
        import clang.cindex
        main()
    except ImportError:
        print("Error: Cannot find clang python library. Please run 'python3 -m pip install libclang'")
        exit()
