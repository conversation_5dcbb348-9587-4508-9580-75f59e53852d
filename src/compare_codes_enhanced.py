import clang.cindex
import json
import os
import shlex
import re

# --- Global Configuration ---
PROJECT_ROOT = '/home/<USER>/openharmony-5.0.0'
COMPARE_FILE = 'device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_scale_node.cpp'
BOARDS_INFO = {
    "rk3568": {
        "host_db_path": 'compile_commands.rk3568.filtered.json',
        "board_path_prefix": "device/board/hihope/rk3568",
        "target_prefixes": [
            "device/board/hihope/rk3568/",
            "vendor/hihope/",
            "device/soc/rockchip/rk3568/",
        ]
    },
    "orangepi_5b": {
        "host_db_path":  'compile_commands.orangepi_5b.filtered.json',
        "board_path_prefix": "device/board/orangepi/orangepi_5b",
        "target_prefixes": [
            "device/board/orangepi/orangepi_5b/",
            "vendor/orangepi/",
            "device/soc/rockchip/rk3588s/",
        ]
    }
}

def analyze_includes(file_path):
    """Analyze file include relationships"""
    includes = {
        'system_includes': [],
        'local_includes': [],
        'project_includes': []
    }

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Match #include statements
        include_pattern = r'#include\s*["<]([^">]+)[">]'
        matches = re.findall(include_pattern, content)

        for include in matches:
            if include.startswith('<') or include.endswith('.h'):
                includes['system_includes'].append(include)
            elif '/' in include and any(prefix in include for prefix in ['device/', 'vendor/', 'drivers/']):
                includes['project_includes'].append(include)
            else:
                includes['local_includes'].append(include)

    except Exception:
        print(f"Error analyzing include files")

    return includes

def analyze_preprocessor_directives(file_path):
    """Analyze preprocessor directives"""
    directives = {
        'defines': [],
        'ifdefs': [],
        'pragmas': []
    }

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Match #define
        define_pattern = r'#define\s+(\w+)'
        directives['defines'] = re.findall(define_pattern, content)

        # Match conditional compilation
        ifdef_pattern = r'#if(?:def|ndef)?\s+(\w+)'
        directives['ifdefs'] = re.findall(ifdef_pattern, content)

        # Match pragma
        pragma_pattern = r'#pragma\s+(.+)'
        directives['pragmas'] = re.findall(pragma_pattern, content)

    except Exception:
        print(f"Error analyzing preprocessor directives")

    return directives

def analyze_complexity(file_path, data_store):
    """Analyze code complexity"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        total_lines = len(lines)
        code_lines = 0
        comment_lines = 0

        in_multiline_comment = False
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue

            # Check multiline comments
            if '/*' in stripped:
                in_multiline_comment = True
            if '*/' in stripped:
                in_multiline_comment = False
                comment_lines += 1
                continue

            if in_multiline_comment:
                comment_lines += 1
            elif stripped.startswith('//'):
                comment_lines += 1
            else:
                code_lines += 1

        data_store['complexity'] = {
            'total_lines': total_lines,
            'code_lines': code_lines,
            'comment_lines': comment_lines
        }
    except Exception:
        print(f"Error analyzing code complexity")

def load_and_filter_db(host_db_path, target_prefixes):
    """Load and filter compilation database, using absolute paths as dictionary keys."""
    print(f"Loading and filtering data from {host_db_path}...")
    try:
        with open(host_db_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: Cannot find file {host_db_path}.")
        return None

    filtered_commands = {}
    print("Starting filtering...")
    for command in data:
        relative_file_path = command.get('file')
        compile_directory = command.get('directory')

        if not relative_file_path or not compile_directory:
            continue

        # Build absolute path
        absolute_file_path = os.path.abspath(os.path.join(compile_directory, relative_file_path))

        # Filter using absolute path
        if any(prefix in absolute_file_path for prefix in target_prefixes):
            filtered_commands[absolute_file_path] = command

    print(f"Filtering completed, found {len(filtered_commands)} related commands.")
    return filtered_commands

def parse_file_enhanced(file_path, command_entry):
    """Enhanced file parsing function with comprehensive analysis"""
    if not command_entry:
        print(f"Warning: Cannot find compilation command for file {file_path}.")
        return None

    # Analyze file static information
    includes = analyze_includes(file_path)
    preprocessor = analyze_preprocessor_directives(file_path)

    original_cwd = os.getcwd()
    try:
        working_dir = command_entry['directory']
        args = shlex.split(command_entry['command'])

        print(f"  - Working directory: {working_dir}")
        os.chdir(working_dir)

        if 'ccache' in os.path.basename(args[0]):
            args = args[1:]

        args_for_clang = args[1:]

        # Create a minimized argument list
        simple_args = []

        # Only keep header file paths and basic definitions
        for arg in args_for_clang:
            if arg.startswith('-I'):
                simple_args.append(arg)
            elif arg.startswith('-D') and not any(skip in arg for skip in ['__DATE__', '__TIME__', '__TIMESTAMP__', '__has_builtin', '__has_feature', '__has_extension', '__has_attribute', '__has_cpp_attribute']):
                simple_args.append(arg)
            elif arg in ['-std=c++17', '-std=c++14', '-std=c++11']:
                simple_args.append(arg)

        # Add necessary system macro definitions (avoid redefining built-in macros)
        system_defines = [
            '-D__GLIBC_PREREQ(maj,min)=1',
            '-D__GLIBC_USE(feature)=1',
            '-D__GNUC_PREREQ(maj,min)=1',
            '-D_GNU_SOURCE=1',
            '-D__STDC_HOSTED__=1',
            '-Drestrict=__restrict__',  # Resolve restrict keyword conflict
            '-D__restrict=__restrict__',
            '-D_Pragma(x)=',  # Ignore pragma directives
        ]

        # Add basic C++ standard and system definitions
        simple_args.extend([
            '-std=c++17',
            '-x', 'c++',
            '-w',  # Disable all warnings
            '-Wno-error',  # Downgrade errors to warnings
            '-fsyntax-only',  # Only perform syntax checking
            '-fno-builtin',  # Disable built-in functions
        ])

        # Add system macro definitions
        simple_args.extend(system_defines)

        # Simplify header file path handling, avoid system path conflicts
        simple_args.extend([
            '-I/usr/include',
            '-isystem', '/usr/include/c++/9',
            '-isystem', '/usr/include/x86_64-linux-gnu/c++/9',
        ])

        print(f"  - Simplified argument count: {len(simple_args)}")
        print(f"  - System macro definition count: {len(system_defines)}")

        # Check if file exists
        if not os.path.exists(file_path):
            print(f"  - Error: File does not exist {file_path}")
            return None

        print(f"  - Starting AST parsing...")

        index = clang.cindex.Index.create()

        # Use optimized parsing options for better performance
        parse_options = (
            clang.cindex.TranslationUnit.PARSE_SKIP_FUNCTION_BODIES |
            clang.cindex.TranslationUnit.PARSE_INCOMPLETE
        )

        tu = index.parse(file_path, args=simple_args, options=parse_options)

        if not tu:
            print(f"  - Error: Cannot create translation unit")
            return None

        print(f"  - Translation unit created successfully")

        # Use enhanced diagnostic information display
        error_count, warning_count = enhanced_diagnostics(tu)

        if error_count > 0:
            print(f"  - Found {error_count} errors, but continuing to extract AST...")

        # Initialize enhanced data storage structure
        data_store = {
            'functions': {},
            'structs': {},
            'classes': {},
            'enums': {},
            'typedefs': {},
            'variables': {},
            'includes': includes,
            'preprocessor': preprocessor,
            'complexity': {
                'total_lines': 0,
                'code_lines': 0,
                'comment_lines': 0
            }
        }

        # Analyze code complexity
        analyze_complexity(file_path, data_store)

        # Traverse AST with enhanced analysis
        traverse_ast_enhanced(tu.cursor, data_store, file_path)

        print(f"  - Successfully extracted: {len(data_store['functions'])} functions, {len(data_store['structs'])} structs, {len(data_store['classes'])} classes")
        return data_store

    except Exception:
        print(f"Exception occurred while processing file {file_path}")
        return None
    finally:
        os.chdir(original_cwd)

def enhanced_diagnostics(tu):
    """Enhanced diagnostic information display"""
    error_count = 0
    warning_count = 0
    print(f"  - Diagnostic summary: {error_count} errors, {warning_count} warnings")

    return error_count, warning_count

def traverse_ast_enhanced(cursor, data_store, source_file_full_path):
    """Enhanced AST traversal with comprehensive analysis"""
    try:
        if cursor.location.file and cursor.location.file.name == source_file_full_path:
            # Function declarations
            if cursor.kind in [clang.cindex.CursorKind.FUNCTION_DECL, clang.cindex.CursorKind.CXX_METHOD]:
                if cursor.spelling:
                    data_store['functions'][cursor.spelling] = {
                        'type': cursor.type.spelling,
                        'line': cursor.location.line,
                        'is_static': cursor.storage_class == clang.cindex.StorageClass.STATIC
                    }

            # Struct declarations
            elif cursor.kind == clang.cindex.CursorKind.STRUCT_DECL and cursor.is_definition():
                if cursor.spelling:
                    fields = []
                    for child in cursor.get_children():
                        if child.kind == clang.cindex.CursorKind.FIELD_DECL:
                            fields.append({
                                'name': child.spelling,
                                'type': child.type.spelling
                            })
                    data_store['structs'][cursor.spelling] = fields

            # Class declarations
            elif cursor.kind == clang.cindex.CursorKind.CLASS_DECL and cursor.is_definition():
                if cursor.spelling:
                    methods = []
                    for child in cursor.get_children():
                        if child.kind == clang.cindex.CursorKind.CXX_METHOD:
                            methods.append({
                                'name': child.spelling,
                                'type': child.type.spelling,
                                'access': child.access_specifier.name if hasattr(child, 'access_specifier') else 'unknown'
                            })
                    data_store['classes'][cursor.spelling] = methods

            # Enum declarations
            elif cursor.kind == clang.cindex.CursorKind.ENUM_DECL and cursor.is_definition():
                if cursor.spelling:
                    enum_values = []
                    for child in cursor.get_children():
                        if child.kind == clang.cindex.CursorKind.ENUM_CONSTANT_DECL:
                            enum_values.append(child.spelling)
                    data_store['enums'][cursor.spelling] = enum_values

            # Type definitions
            elif cursor.kind == clang.cindex.CursorKind.TYPEDEF_DECL:
                if cursor.spelling:
                    data_store['typedefs'][cursor.spelling] = cursor.type.spelling

            # Global variables
            elif cursor.kind == clang.cindex.CursorKind.VAR_DECL:
                if cursor.spelling and cursor.linkage == clang.cindex.LinkageKind.EXTERNAL:
                    data_store['variables'][cursor.spelling] = cursor.type.spelling

        # Recursively traverse child nodes
        for child in cursor.get_children():
            traverse_ast_enhanced(child, data_store, source_file_full_path)
    except Exception:
        # Ignore errors during traversal, continue processing other nodes
        pass

def compare_enhanced_data(data1, data2, name1, name2):
    """Enhanced data comparison with comprehensive analysis"""
    print("\n" + "="*50 + " Detailed Code Difference Report " + "="*50)

    # 1. Code complexity comparison
    print("\n--- Code Complexity Comparison ---")
    c1, c2 = data1['complexity'], data2['complexity']
    print(f"{name1}: Total lines {c1['total_lines']}, Code lines {c1['code_lines']}, Comment lines {c1['comment_lines']}")
    print(f"{name2}: Total lines {c2['total_lines']}, Code lines {c2['code_lines']}, Comment lines {c2['comment_lines']}")

    # 2. Include files comparison
    print("\n--- Include Files Comparison ---")
    inc1, inc2 = set(data1['includes']['project_includes']), set(data2['includes']['project_includes'])
    for inc in sorted(inc1 - inc2):
        print(f"(+) {name1} unique include: {inc}")
    for inc in sorted(inc2 - inc1):
        print(f"(-) {name2} unique include: {inc}")

    # 3. Enhanced function comparison
    print("\n--- Function Comparison ---")
    funcs1, funcs2 = set(data1['functions'].keys()), set(data2['functions'].keys())
    for f in sorted(funcs1 - funcs2):
        func_info = data1['functions'][f]
        print(f"(+) {name1} unique function: {f} (line: {func_info['line']})")
    for f in sorted(funcs2 - funcs1):
        func_info = data2['functions'][f]
        print(f"(-) {name2} unique function: {f} (line: {func_info['line']})")
    for f in sorted(funcs1 & funcs2):
        if data1['functions'][f]['type'] != data2['functions'][f]['type']:
            print(f"(*) Function signature changed: {f}")
            print(f"      {name1}: {data1['functions'][f]['type']}")
            print(f"      {name2}: {data2['functions'][f]['type']}")

    # 4. Struct comparison
    print("\n--- Struct Comparison ---")
    structs1, structs2 = set(data1['structs'].keys()), set(data2['structs'].keys())
    for s in sorted(structs1 - structs2):
        print(f"(+) {name1} unique struct: {s}")
    for s in sorted(structs2 - structs1):
        print(f"(-) {name2} unique struct: {s}")
    for s in sorted(structs1 & structs2):
        if data1['structs'][s] != data2['structs'][s]:
            print(f"(*) Struct changed: {s}")

    # 5. Class comparison
    print("\n--- Class Comparison ---")
    classes1, classes2 = set(data1['classes'].keys()), set(data2['classes'].keys())
    for c in sorted(classes1 - classes2):
        print(f"(+) {name1} unique class: {c}")
    for c in sorted(classes2 - classes1):
        print(f"(-) {name2} unique class: {c}")

    # 6. Enum comparison
    print("\n--- Enum Comparison ---")
    enums1, enums2 = set(data1['enums'].keys()), set(data2['enums'].keys())
    for e in sorted(enums1 - enums2):
        print(f"(+) {name1} unique enum: {e}")
    for e in sorted(enums2 - enums1):
        print(f"(-) {name2} unique enum: {e}")

    # 7. Preprocessor directives comparison
    print("\n--- Preprocessor Directives Comparison ---")
    defs1, defs2 = set(data1['preprocessor']['defines']), set(data2['preprocessor']['defines'])
    for d in sorted(defs1 - defs2):
        print(f"(+) {name1} unique macro definition: {d}")
    for d in sorted(defs2 - defs1):
        print(f"(-) {name2} unique macro definition: {d}")

    print("-" * 80)

def main():
    board1_name = "rk3568"
    board2_name = "orangepi_5b"

    db1 = load_and_filter_db(BOARDS_INFO[board1_name]['host_db_path'], BOARDS_INFO[board1_name]['target_prefixes'])
    db2 = load_and_filter_db(BOARDS_INFO[board2_name]['host_db_path'], BOARDS_INFO[board2_name]['target_prefixes'])

    if not db1 or not db2:
        print("Database loading failed, cannot continue.")
        return

    file_to_compare_b1 = os.path.join(PROJECT_ROOT, COMPARE_FILE)

    prefix1 = BOARDS_INFO[board1_name]['board_path_prefix']
    prefix2 = BOARDS_INFO[board2_name]['board_path_prefix']
    file_to_compare_b2 = file_to_compare_b1.replace(prefix1, prefix2)

    print("\n" + "="*20 + " Starting Enhanced File Comparison " + "="*20)
    print(f"File to compare 1: {file_to_compare_b1}")
    print(f"File to compare 2: {file_to_compare_b2}")

    print(f"\nAnalyzing {board1_name}...")
    info1 = parse_file_enhanced(file_to_compare_b1, db1.get(file_to_compare_b1))

    print(f"\nAnalyzing {board2_name}...")
    info2 = parse_file_enhanced(file_to_compare_b2, db2.get(file_to_compare_b2))

    if info1 and info2:
        compare_enhanced_data(info1, info2, board1_name, board2_name)
    elif info1:
        print(f"\nOnly successfully parsed {board1_name} file, cannot compare.")
        print(f"{board1_name} contains {len(info1['functions'])} functions, {len(info1['structs'])} structs, {len(info1['classes'])} classes.")
    elif info2:
        print(f"\nOnly successfully parsed {board2_name} file, cannot compare.")
        print(f"{board2_name} contains {len(info2['functions'])} functions, {len(info2['structs'])} structs, {len(info2['classes'])} classes.")
    else:
        print("\nFailed to parse any files, cannot compare.")


if __name__ == '__main__':
    try:
        import clang.cindex
        main()
    except ImportError:
        print("Error: Cannot find clang python library. Please run 'python3 -m pip install libclang'")
        exit()
