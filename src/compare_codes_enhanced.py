import clang.cindex
import json
import os
import shlex

# --- 全局配置 ---
PROJECT_ROOT = '/home/<USER>/openharmony-5.0.0'

BOARDS_INFO = {
    "rk3568": {
        "host_db_path": 'compile_commands.rk3568.filtered.json',
        "board_path_prefix": "device/board/hihope/rk3568",
        "target_prefixes": [
            "device/board/hihope/rk3568/",
            "vendor/hihope/",
            "device/soc/rockchip/rk3568/",
        ]
    },
    "orangepi_5b": {
        "host_db_path":  'compile_commands.orangepi_5b.filtered.json',
        "board_path_prefix": "device/board/orangepi/orangepi_5b",
        "target_prefixes": [
            "device/board/orangepi/orangepi_5b/",
            "vendor/orangepi/",
            "device/soc/rockchip/rk3588s/",
        ]
    }
}

def load_and_filter_db(host_db_path, target_prefixes):
    """加载并过滤编译数据库，使用绝对路径作为字典的键。"""
    print(f"正在从 {host_db_path} 加载并过滤数据...")
    try:
        with open(host_db_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到经路径翻译后的文件 {host_db_path}。")
        return None

    filtered_commands = {}
    print("开始筛选...")
    for command in data:
        relative_file_path = command.get('file')
        compile_directory = command.get('directory')

        if not relative_file_path or not compile_directory:
            continue
        
        # 构建绝对路径
        absolute_file_path = os.path.abspath(os.path.join(compile_directory, relative_file_path))

        # 使用绝对路径进行过滤
        if any(prefix in absolute_file_path for prefix in target_prefixes):
            filtered_commands[absolute_file_path] = command
            
    print(f"筛选完成，找到 {len(filtered_commands)} 条相关命令。")
    return filtered_commands

def parse_file_simple(file_path, command_entry):
    """简化版本的文件解析函数"""
    if not command_entry:
        print(f"警告: 找不到文件 {file_path} 的编译命令。")
        return None

    original_cwd = os.getcwd()
    try:
        working_dir = command_entry['directory']
        args = shlex.split(command_entry['command'])
        
        print(f"  - 工作目录: {working_dir}")
        os.chdir(working_dir)

        if 'ccache' in os.path.basename(args[0]):
            args = args[1:]
        
        args_for_clang = args[1:]
        
        # 创建一个最简化的参数列表
        simple_args = []
        
        # 只保留头文件路径和基本定义
        for arg in args_for_clang:
            if arg.startswith('-I'):
                simple_args.append(arg)
            elif arg.startswith('-D') and not any(skip in arg for skip in ['__DATE__', '__TIME__', '__TIMESTAMP__', '__has_builtin', '__has_feature', '__has_extension', '__has_attribute', '__has_cpp_attribute']):
                simple_args.append(arg)
            elif arg in ['-std=c++17', '-std=c++14', '-std=c++11']:
                simple_args.append(arg)
        
        # 添加必要的系统宏定义（避免重定义内置宏）
        system_defines = [
            '-D__GLIBC_PREREQ(maj,min)=1',
            '-D__GLIBC_USE(feature)=1',
            '-D__GNUC_PREREQ(maj,min)=1',
            '-D_GNU_SOURCE=1',
            '-D__STDC_HOSTED__=1',
            '-Drestrict=__restrict__',  # 解决 restrict 关键字冲突
            '-D__restrict=__restrict__',
            '-D_Pragma(x)=',  # 忽略 pragma 指令
        ]
        
        # 添加基本的 C++ 标准和系统定义
        simple_args.extend([
            '-std=c++17',
            '-x', 'c++',
            '-w',  # 禁用所有警告
            '-Wno-error',  # 将错误降级为警告
            '-fsyntax-only',  # 只进行语法检查
            '-fno-builtin',  # 禁用内置函数
        ])
        
        # 添加系统宏定义
        simple_args.extend(system_defines)
        
        # 简化头文件路径处理，避免系统路径冲突
        # 使用备用路径，但排除可能有冲突的系统路径
        simple_args.extend([
            '-I/usr/include',
            '-isystem', '/usr/include/c++/9',
            '-isystem', '/usr/include/x86_64-linux-gnu/c++/9',
        ])
        
        print(f"  - 简化参数数量: {len(simple_args)}")
        print(f"  - 系统宏定义数量: {len(system_defines)}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"  - 错误: 文件不存在 {file_path}")
            return None

        print(f"  - 开始解析...")

        index = clang.cindex.Index.create()
        
        # 使用最宽松的解析选项
        parse_options = (
            clang.cindex.TranslationUnit.PARSE_DETAILED_PROCESSING_RECORD |
            clang.cindex.TranslationUnit.PARSE_INCOMPLETE
        )
        
        tu = index.parse(file_path, args=simple_args, options=parse_options)

        if not tu:
            print(f"  - 错误: 无法创建翻译单元")
            return None

        print(f"  - 翻译单元创建成功")

        # 使用增强的诊断信息显示
        error_count, warning_count = enhanced_diagnostics(tu)
        
        if error_count > 0:
            print(f"  - 发现 {error_count} 个错误，但继续尝试提取AST...")

        data_store = {'functions': {}, 'structs': {}, 'classes': {}}
        traverse_ast(tu.cursor, data_store, file_path)
        print(f"  - 成功提取: {len(data_store['functions'])}个函数, {len(data_store['structs'])}个结构体。")
        return data_store
        
    except Exception as e:
        print(f"处理文件 {file_path} 时发生异常: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        os.chdir(original_cwd)

def enhanced_diagnostics(tu):
    """增强的诊断信息显示"""
    error_count = 0
    warning_count = 0
    
    print("  - 详细诊断信息:")
    for diag in tu.diagnostics:
        severity_name = {
            0: "忽略",    # Diagnostic.Ignored
            1: "注释",    # Diagnostic.Note 
            2: "警告",    # Diagnostic.Warning
            3: "错误",    # Diagnostic.Error
            4: "致命错误" # Diagnostic.Fatal
        }.get(diag.severity, "未知")
        
        location = f"{diag.location.file.name if diag.location.file else '未知文件'}:{diag.location.line}:{diag.location.column}"
        
        if diag.severity >= 3:  # Error level
            error_count += 1
            print(f"    [{severity_name}] {location}: {diag.spelling}")
        elif diag.severity == 2:  # Warning level
            warning_count += 1
            if warning_count <= 5:  # 只显示前5个警告
                print(f"    [{severity_name}] {location}: {diag.spelling}")
    
    if warning_count > 5:
        print(f"    ... 还有 {warning_count - 5} 个警告未显示")
        
    print(f"  - 诊断统计: {error_count} 个错误, {warning_count} 个警告")
    
    return error_count, warning_count

def traverse_ast(cursor, data_store, source_file_full_path):
    """遍历AST并提取信息"""
    try:
        if cursor.location.file and cursor.location.file.name == source_file_full_path:
            if cursor.kind in [clang.cindex.CursorKind.FUNCTION_DECL, clang.cindex.CursorKind.CXX_METHOD]:
                if cursor.spelling:  # 确保函数名不为空
                    data_store['functions'][cursor.spelling] = cursor.type.spelling
            elif cursor.kind == clang.cindex.CursorKind.STRUCT_DECL and cursor.is_definition():
                if cursor.spelling:
                    fields = [f.spelling for f in cursor.get_children() if f.kind == clang.cindex.CursorKind.FIELD_DECL]
                    data_store['structs'][cursor.spelling] = sorted(fields)
        
        # 递归遍历子节点
        for child in cursor.get_children():
            traverse_ast(child, data_store, source_file_full_path)
    except Exception as e:
        # 忽略遍历过程中的错误，继续处理其他节点
        pass

def compare_ast_data(data1, data2, name1, name2):
    print("\n" + "="*20 + " 代码结构差异报告 " + "="*20)
    funcs1, funcs2 = set(data1['functions']), set(data2['functions'])
    print("--- 函数对比 ---")
    for f in sorted(funcs1 - funcs2): 
        print(f"(+) {name1} 独有函数: {f}")
    for f in sorted(funcs2 - funcs1): 
        print(f"(-) {name2} 独有函数: {f}")
    for f in sorted(funcs1 & funcs2):
        if data1['functions'][f] != data2['functions'][f]: 
            print(f"(*) 函数签名已变更: {f}")
            print(f"      {name1}: {data1['functions'][f]}")
            print(f"      {name2}: {data2['functions'][f]}")
    
    structs1, structs2 = set(data1['structs']), set(data2['structs'])
    print("\n--- 结构体对比 ---")
    for s in sorted(structs1 - structs2): 
        print(f"(+) {name1} 独有结构体: {s}")
    for s in sorted(structs2 - structs1): 
        print(f"(-) {name2} 独有结构体: {s}")
    for s in sorted(structs1 & structs2):
        if data1['structs'][s] != data2['structs'][s]: 
            print(f"(*) 结构体已变更: {s}")
            print(f"      {name1}: {data1['structs'][s]}")
            print(f"      {name2}: {data2['structs'][s]}")
    print("-" * 50)

def main():
    board1_name = "rk3568"
    board2_name = "orangepi_5b"
    
    db1 = load_and_filter_db(BOARDS_INFO[board1_name]['host_db_path'], BOARDS_INFO[board1_name]['target_prefixes'])
    db2 = load_and_filter_db(BOARDS_INFO[board2_name]['host_db_path'], BOARDS_INFO[board2_name]['target_prefixes'])
    
    if not db1 or not db2:
        print("数据库加载失败，无法继续。")
        return

    file_to_compare_b1 = os.path.join(PROJECT_ROOT, 'device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_codec_node.cpp')
    
    prefix1 = BOARDS_INFO[board1_name]['board_path_prefix']
    prefix2 = BOARDS_INFO[board2_name]['board_path_prefix']
    file_to_compare_b2 = file_to_compare_b1.replace(prefix1, prefix2)
    
    print("\n" + "="*20 + " 开始文件对比 " + "="*20)
    print(f"要对比的文件1: {file_to_compare_b1}")
    print(f"要对比的文件2: {file_to_compare_b2}")
    
    print(f"\n分析 {board1_name}...")
    info1 = parse_file_simple(file_to_compare_b1, db1.get(file_to_compare_b1))
    
    print(f"\n分析 {board2_name}...")
    info2 = parse_file_simple(file_to_compare_b2, db2.get(file_to_compare_b2))

    if info1 and info2:
        compare_ast_data(info1, info2, board1_name, board2_name)
    elif info1:
        print(f"\n只成功解析了 {board1_name} 的文件，无法进行对比。")
        print(f"{board1_name} 包含 {len(info1['functions'])} 个函数，{len(info1['structs'])} 个结构体。")
    elif info2:
        print(f"\n只成功解析了 {board2_name} 的文件，无法进行对比。")
        print(f"{board2_name} 包含 {len(info2['functions'])} 个函数，{len(info2['structs'])} 个结构体。")
    else:
        print("\n未能成功解析任何文件，无法进行对比。")


if __name__ == '__main__':
    try:
        import clang.cindex
        main()
    except ImportError:
        print("错误: 找不到 clang python 库。请运行 'python3 -m pip install libclang'")
        exit()
