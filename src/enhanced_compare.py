import clang.cindex
import json
import os
import shlex
import re

# --- 全局配置 ---
PROJECT_ROOT = '/home/<USER>/openharmony-5.0.0'

BOARDS_INFO = {
    "rk3568": {
        "host_db_path": 'compile_commands.rk3568.filtered.json',
        "board_path_prefix": "device/board/hihope/rk3568",
        "target_prefixes": [
            "device/board/hihope/rk3568/",
            "vendor/hihope/",
            "device/soc/rockchip/rk3568/",
        ]
    },
    "orangepi_5b": {
        "host_db_path":  'compile_commands.orangepi_5b.filtered.json',
        "board_path_prefix": "device/board/orangepi/orangepi_5b",
        "target_prefixes": [
            "device/board/orangepi/orangepi_5b/",
            "vendor/orangepi/",
            "device/soc/rockchip/rk3588s/",
        ]
    }
}

def analyze_includes(file_path):
    """分析文件的包含关系"""
    includes = {
        'system_includes': [],
        'local_includes': [],
        'project_includes': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        # 匹配 #include 语句
        include_pattern = r'#include\s*["<]([^">]+)[">]'
        matches = re.findall(include_pattern, content)
        
        for include in matches:
            if include.startswith('<') or include.endswith('.h'):
                includes['system_includes'].append(include)
            elif '/' in include and any(prefix in include for prefix in ['device/', 'vendor/', 'drivers/']):
                includes['project_includes'].append(include)
            else:
                includes['local_includes'].append(include)
                
    except Exception as e:
        print(f"分析包含文件时出错: {e}")
    
    return includes

def analyze_preprocessor_directives(file_path):
    """分析预处理器指令"""
    directives = {
        'defines': [],
        'ifdefs': [],
        'pragmas': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        # 匹配 #define
        define_pattern = r'#define\s+(\w+)'
        directives['defines'] = re.findall(define_pattern, content)
        
        # 匹配条件编译
        ifdef_pattern = r'#if(?:def|ndef)?\s+(\w+)'
        directives['ifdefs'] = re.findall(ifdef_pattern, content)
        
        # 匹配 pragma
        pragma_pattern = r'#pragma\s+(.+)'
        directives['pragmas'] = re.findall(pragma_pattern, content)
        
    except Exception as e:
        print(f"分析预处理器指令时出错: {e}")
    
    return directives

def load_and_filter_db(host_db_path, target_prefixes):
    """加载并过滤编译数据库"""
    print(f"正在从 {host_db_path} 加载并过滤数据...")
    try:
        with open(host_db_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {host_db_path}。")
        return None

    filtered_commands = {}
    print("开始筛选...")
    for command in data:
        relative_file_path = command.get('file')
        compile_directory = command.get('directory')

        if not relative_file_path or not compile_directory:
            continue
        
        absolute_file_path = os.path.abspath(os.path.join(compile_directory, relative_file_path))

        if any(prefix in absolute_file_path for prefix in target_prefixes):
            filtered_commands[absolute_file_path] = command
            
    print(f"筛选完成，找到 {len(filtered_commands)} 条相关命令。")
    return filtered_commands

def parse_file_enhanced(file_path, command_entry):
    """增强版本的文件解析函数"""
    if not command_entry:
        print(f"警告: 找不到文件 {file_path} 的编译命令。")
        return None

    # 分析文件的静态信息
    includes = analyze_includes(file_path)
    preprocessor = analyze_preprocessor_directives(file_path)
    
    original_cwd = os.getcwd()
    try:
        working_dir = command_entry['directory']
        args = shlex.split(command_entry['command'])
        
        print(f"  - 工作目录: {working_dir}")
        os.chdir(working_dir)

        if 'ccache' in os.path.basename(args[0]):
            args = args[1:]
        
        args_for_clang = args[1:]
        
        # 创建简化的参数列表
        simple_args = []
        for arg in args_for_clang:
            if arg.startswith('-I'):
                simple_args.append(arg)
            elif arg.startswith('-D') and not any(skip in arg for skip in ['__DATE__', '__TIME__', '__TIMESTAMP__']):
                simple_args.append(arg)
            elif arg in ['-std=c++17', '-std=c++14', '-std=c++11']:
                simple_args.append(arg)
        
        simple_args.extend([
            '-std=c++17',
            '-I/usr/include',
            '-I/usr/include/c++/9',
            '-I/usr/include/x86_64-linux-gnu/c++/9',
        ])
        
        print(f"  - 简化参数数量: {len(simple_args)}")

        if not os.path.exists(file_path):
            print(f"  - 错误: 文件不存在 {file_path}")
            return None

        print(f"  - 开始AST解析...")

        index = clang.cindex.Index.create()
        parse_options = (
            clang.cindex.TranslationUnit.PARSE_SKIP_FUNCTION_BODIES |
            clang.cindex.TranslationUnit.PARSE_INCOMPLETE
        )
        
        tu = index.parse(file_path, args=simple_args, options=parse_options)

        if not tu:
            print(f"  - 错误: 无法创建翻译单元")
            return None

        print(f"  - 翻译单元创建成功")

        # 初始化数据存储结构
        data_store = {
            'functions': {},
            'structs': {},
            'classes': {},
            'enums': {},
            'typedefs': {},
            'variables': {},
            'includes': includes,
            'preprocessor': preprocessor,
            'complexity': {
                'total_lines': 0,
                'code_lines': 0,
                'comment_lines': 0
            }
        }
        
        # 分析代码复杂度
        analyze_complexity(file_path, data_store)
        
        # 遍历AST
        traverse_ast_enhanced(tu.cursor, data_store, file_path)
        
        print(f"  - 成功提取: {len(data_store['functions'])}个函数, {len(data_store['structs'])}个结构体, {len(data_store['classes'])}个类")
        return data_store
        
    except Exception as e:
        print(f"处理文件 {file_path} 时发生异常: {e}")
        return None
    finally:
        os.chdir(original_cwd)

def analyze_complexity(file_path, data_store):
    """分析代码复杂度"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        code_lines = 0
        comment_lines = 0
        
        in_multiline_comment = False
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
            
            # 检查多行注释
            if '/*' in stripped:
                in_multiline_comment = True
            if '*/' in stripped:
                in_multiline_comment = False
                comment_lines += 1
                continue
            
            if in_multiline_comment:
                comment_lines += 1
            elif stripped.startswith('//'):
                comment_lines += 1
            else:
                code_lines += 1
        
        data_store['complexity'] = {
            'total_lines': total_lines,
            'code_lines': code_lines,
            'comment_lines': comment_lines
        }
    except Exception as e:
        print(f"分析代码复杂度时出错: {e}")

def traverse_ast_enhanced(cursor, data_store, source_file_full_path):
    """增强版AST遍历"""
    try:
        if cursor.location.file and cursor.location.file.name == source_file_full_path:
            # 函数声明
            if cursor.kind in [clang.cindex.CursorKind.FUNCTION_DECL, clang.cindex.CursorKind.CXX_METHOD]:
                if cursor.spelling:
                    data_store['functions'][cursor.spelling] = {
                        'type': cursor.type.spelling,
                        'line': cursor.location.line,
                        'is_static': cursor.storage_class == clang.cindex.StorageClass.STATIC
                    }
            
            # 结构体声明
            elif cursor.kind == clang.cindex.CursorKind.STRUCT_DECL and cursor.is_definition():
                if cursor.spelling:
                    fields = []
                    for child in cursor.get_children():
                        if child.kind == clang.cindex.CursorKind.FIELD_DECL:
                            fields.append({
                                'name': child.spelling,
                                'type': child.type.spelling
                            })
                    data_store['structs'][cursor.spelling] = fields
            
            # 类声明
            elif cursor.kind == clang.cindex.CursorKind.CLASS_DECL and cursor.is_definition():
                if cursor.spelling:
                    methods = []
                    for child in cursor.get_children():
                        if child.kind == clang.cindex.CursorKind.CXX_METHOD:
                            methods.append({
                                'name': child.spelling,
                                'type': child.type.spelling,
                                'access': child.access_specifier.name if hasattr(child, 'access_specifier') else 'unknown'
                            })
                    data_store['classes'][cursor.spelling] = methods
            
            # 枚举声明
            elif cursor.kind == clang.cindex.CursorKind.ENUM_DECL and cursor.is_definition():
                if cursor.spelling:
                    enum_values = []
                    for child in cursor.get_children():
                        if child.kind == clang.cindex.CursorKind.ENUM_CONSTANT_DECL:
                            enum_values.append(child.spelling)
                    data_store['enums'][cursor.spelling] = enum_values
            
            # 类型定义
            elif cursor.kind == clang.cindex.CursorKind.TYPEDEF_DECL:
                if cursor.spelling:
                    data_store['typedefs'][cursor.spelling] = cursor.type.spelling
            
            # 全局变量
            elif cursor.kind == clang.cindex.CursorKind.VAR_DECL:
                if cursor.spelling and cursor.linkage == clang.cindex.LinkageKind.EXTERNAL:
                    data_store['variables'][cursor.spelling] = cursor.type.spelling
        
        # 递归遍历子节点
        for child in cursor.get_children():
            traverse_ast_enhanced(child, data_store, source_file_full_path)
    except Exception as e:
        pass

def compare_enhanced_data(data1, data2, name1, name2):
    """增强版数据对比"""
    print("\n" + "="*50 + " 详细代码差异报告 " + "="*50)
    
    # 1. 代码复杂度对比
    print("\n--- 代码复杂度对比 ---")
    c1, c2 = data1['complexity'], data2['complexity']
    print(f"{name1}: 总行数 {c1['total_lines']}, 代码行数 {c1['code_lines']}, 注释行数 {c1['comment_lines']}")
    print(f"{name2}: 总行数 {c2['total_lines']}, 代码行数 {c2['code_lines']}, 注释行数 {c2['comment_lines']}")
    
    # 2. 包含文件对比
    print("\n--- 包含文件对比 ---")
    inc1, inc2 = set(data1['includes']['project_includes']), set(data2['includes']['project_includes'])
    for inc in sorted(inc1 - inc2):
        print(f"(+) {name1} 独有包含: {inc}")
    for inc in sorted(inc2 - inc1):
        print(f"(-) {name2} 独有包含: {inc}")
    
    # 3. 函数对比（增强）
    print("\n--- 函数对比 ---")
    funcs1, funcs2 = set(data1['functions'].keys()), set(data2['functions'].keys())
    for f in sorted(funcs1 - funcs2):
        func_info = data1['functions'][f]
        print(f"(+) {name1} 独有函数: {f} (行号: {func_info['line']})")
    for f in sorted(funcs2 - funcs1):
        func_info = data2['functions'][f]
        print(f"(-) {name2} 独有函数: {f} (行号: {func_info['line']})")
    
    # 4. 结构体对比
    print("\n--- 结构体对比 ---")
    structs1, structs2 = set(data1['structs'].keys()), set(data2['structs'].keys())
    for s in sorted(structs1 - structs2):
        print(f"(+) {name1} 独有结构体: {s}")
    for s in sorted(structs2 - structs1):
        print(f"(-) {name2} 独有结构体: {s}")
    
    # 5. 类对比
    print("\n--- 类对比 ---")
    classes1, classes2 = set(data1['classes'].keys()), set(data2['classes'].keys())
    for c in sorted(classes1 - classes2):
        print(f"(+) {name1} 独有类: {c}")
    for c in sorted(classes2 - classes1):
        print(f"(-) {name2} 独有类: {c}")
    
    # 6. 枚举对比
    print("\n--- 枚举对比 ---")
    enums1, enums2 = set(data1['enums'].keys()), set(data2['enums'].keys())
    for e in sorted(enums1 - enums2):
        print(f"(+) {name1} 独有枚举: {e}")
    for e in sorted(enums2 - enums1):
        print(f"(-) {name2} 独有枚举: {e}")
    
    # 7. 预处理器指令对比
    print("\n--- 预处理器指令对比 ---")
    defs1, defs2 = set(data1['preprocessor']['defines']), set(data2['preprocessor']['defines'])
    for d in sorted(defs1 - defs2):
        print(f"(+) {name1} 独有宏定义: {d}")
    for d in sorted(defs2 - defs1):
        print(f"(-) {name2} 独有宏定义: {d}")
    
    print("-" * 80)

def main():
    board1_name = "rk3568"
    board2_name = "orangepi_5b"
    
    db1 = load_and_filter_db(BOARDS_INFO[board1_name]['host_db_path'], BOARDS_INFO[board1_name]['target_prefixes'])
    db2 = load_and_filter_db(BOARDS_INFO[board2_name]['host_db_path'], BOARDS_INFO[board2_name]['target_prefixes'])
    
    if not db1 or not db2:
        print("数据库加载失败，无法继续。")
        return

    file_to_compare_b1 = os.path.join(PROJECT_ROOT, 'device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_scale_node.cpp')
    
    prefix1 = BOARDS_INFO[board1_name]['board_path_prefix']
    prefix2 = BOARDS_INFO[board2_name]['board_path_prefix']
    file_to_compare_b2 = file_to_compare_b1.replace(prefix1, prefix2)
    
    print("\n" + "="*20 + " 开始增强文件对比 " + "="*20)
    print(f"要对比的文件1: {file_to_compare_b1}")
    print(f"要对比的文件2: {file_to_compare_b2}")
    
    print(f"\n分析 {board1_name}...")
    info1 = parse_file_enhanced(file_to_compare_b1, db1.get(file_to_compare_b1))
    
    print(f"\n分析 {board2_name}...")
    info2 = parse_file_enhanced(file_to_compare_b2, db2.get(file_to_compare_b2))

    if info1 and info2:
        compare_enhanced_data(info1, info2, board1_name, board2_name)
    else:
        print("\n未能成功解析文件，无法进行对比。")

if __name__ == '__main__':
    try:
        import clang.cindex
        main()
    except ImportError:
        print("错误: 找不到 clang python 库。请运行 'python3 -m pip install libclang'")
        exit()
