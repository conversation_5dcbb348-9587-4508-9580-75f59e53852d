import json
import os

# --- 全局配置 ---
# 只需要在这里修改你的项目根目录
PROJECT_ROOT = '/home/<USER>/ohos_5.0/openharmony-5.0.0'

# --- 各个板卡的详细配置 ---
# 在这里添加或修改你想要处理的板卡信息
# 键名（如 "rk3568"）将用于生成输出文件名
BOARDS_CONFIG = {
    "rk3568": {
        "out_dir_name": "rk3568", # OpenHarmony 输出目录名
        "target_prefixes": [     # 与该板卡相关的代码路径前缀
            "device/board/hihope/rk3568/",
            "vendor/hihope/",
            "device/soc/rockchip/rk3568/",
        ]
    },
    "orangepi_5b": {
        "out_dir_name": "orangepi_5b", # OpenHarmony 输出目录名
        "target_prefixes": [          # 与该板卡相关的代码路径前缀
            "device/board/orangepi/orangepi_5b/",
            "vendor/orangepi/",
            # orangepi_5b 使用的也是 rk3588，你可能也需要 soc 目录
            "device/soc/rockchip/rk3588s/",
        ]
    },
    # 如果有第三个板卡，在这里继续添加
    # "another_board": { ... }
}

def filter_one_board(board_name, source_json_path, target_prefixes, output_json_path):
    """
    对单个板卡的 compile_commands.json 进行过滤。

    Args:
        board_name (str): 板卡名称，用于打印日志。
        source_json_path (str): 原始的 compile_commands.json 文件路径。
        target_prefixes (list): 要筛选的目录前缀列表。
        output_json_path (str): 过滤后输出的 json 文件路径。
    """
    print(f"--- 开始处理板卡: {board_name} ---")
    print(f"正在从 {source_json_path} 读取数据...")
    try:
        with open(source_json_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {source_json_path}，跳过此板卡。")
        print("-" * (20 + len(board_name)))
        return

    filtered_commands = []
    print("开始筛选...")
    for command in data:
        file_path = command.get('file')
        if not file_path:
            continue

        # 核心逻辑：移除路径开头的 ../
        cleaned_path = file_path
        while cleaned_path.startswith('../'):
            cleaned_path = cleaned_path[3:]  # 去掉前三个字符 "../"

        if any(cleaned_path.startswith(prefix) for prefix in target_prefixes):
            filtered_commands.append(command)
    
    print(f"筛选完成，找到 {len(filtered_commands)} 条命令。")
    print(f"正在写入到 {output_json_path}...")
    with open(output_json_path, 'w') as f:
        json.dump(filtered_commands, f, indent=2)

    print(f"成功生成文件: {output_json_path}")
    print(f"--- 板卡: {board_name} 处理完成 ---\n")

def main():
    """
    主函数，遍历所有配置好的板卡并进行处理。
    """
    if not os.path.isdir(PROJECT_ROOT):
        print(f"错误：项目根目录 '{PROJECT_ROOT}' 不存在，请检查配置。")
        return

    for board_name, config in BOARDS_CONFIG.items():
        out_dir = os.path.join(PROJECT_ROOT, 'out', config['out_dir_name'])
        source_json = os.path.join(out_dir, 'compile_commands.json')
        
        output_json = f'compile_commands.{board_name}.filtered.json'

        filter_one_board(
            board_name=board_name,
            source_json_path=source_json,
            target_prefixes=config['target_prefixes'],
            output_json_path=output_json
        )

if __name__ == '__main__':
    main()