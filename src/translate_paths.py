import json
import os

# --- 配置您的路径映射 ---
# 这是在 Docker 容器内部的项目根目录
CONTAINER_PROJECT_ROOT = '/home/<USER>/openharmony-5.0.0'
# 这是在您本地宿主机上的项目根目录
HOST_PROJECT_ROOT = '/home/<USER>/ohos_5.0/openharmony-5.0.0'

# 输入：从容器中拷贝出来的原始 compile_commands.json
# 请注意，这个路径是您在宿主机上的路径
SOURCE_JSON = os.path.join('./compile_commands.rk3568.filtered.json')

# 输出：一个全新的、路径被翻译过的、可在宿主机上使用的新文件
OUTPUT_JSON = os.path.join('./compile_commands.rk3568.host.json')

def translate_compile_commands():
    print(f"开始翻译路径...")
    print(f"容器路径前缀: {CONTAINER_PROJECT_ROOT}")
    print(f"宿主机路径前缀: {HOST_PROJECT_ROOT}")
    
    print(f"\n正在从 {SOURCE_JSON} 读取数据...")
    try:
        with open(SOURCE_JSON, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到原始文件 {SOURCE_JSON}")
        print("请确认该文件已从 Docker 环境拷贝至宿主机对应位置。")
        return

    translated_commands = []
    print("正在逐条翻译编译命令...")
    for entry in data:
        # 复制一份原始条目，避免修改原数据
        new_entry = entry.copy()

        # 1. 翻译 'directory' 字段
        if 'directory' in new_entry:
            new_entry['directory'] = new_entry['directory'].replace(
                CONTAINER_PROJECT_ROOT, HOST_PROJECT_ROOT, 1 # 只替换一次
            )

        # 2. 翻译 'command' 字段中的所有路径
        # 这是最关键的一步，因为它包含了所有的 -I, --sysroot 等路径
        if 'command' in new_entry:
            new_entry['command'] = new_entry['command'].replace(
                CONTAINER_PROJECT_ROOT, HOST_PROJECT_ROOT
            )
        
        # 3. 翻译 'file' 字段（虽然它是相对路径，但以防万一有绝对路径的情况）
        if 'file' in new_entry and new_entry['file'].startswith(CONTAINER_PROJECT_ROOT):
             new_entry['file'] = new_entry['file'].replace(
                CONTAINER_PROJECT_ROOT, HOST_PROJECT_ROOT, 1
            )

        translated_commands.append(new_entry)

    print(f"\n翻译完成，共处理 {len(translated_commands)} 条命令。")
    print(f"正在写入到新的文件: {OUTPUT_JSON}")
    with open(OUTPUT_JSON, 'w') as f:
        json.dump(translated_commands, f, indent=2)

    print("\n完成！现在您可以在本地环境中使用新生成的 `compile_commands.host.json` 文件了。")

if __name__ == '__main__':
    translate_compile_commands()