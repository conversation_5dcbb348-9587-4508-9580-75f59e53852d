import clang.cindex
import os
import shlex

# --- 1. 精确、写死的配置 ---
LIBCLANG_PATH = '/home/<USER>/ohos_5.0/openharmony-5.0.0/prebuilts/clang/ohos/linux-x86_64/llvm/lib/libclang.so'
FILE_TO_PARSE = '/home/<USER>/ohos_5.0/openharmony-5.0.0/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_codec_node.cpp'
COMMAND_STR = "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_codec_node.o.d -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -DHAVE_UNSIGNED_SHORT=1 -DNEON_INTRINSICS -DC_ARITH_CODING_SUPPORTED=1 -DD_ARITH_CODING_SUPPORTED=1 -DBMP_SUPPORTED=1 -DPPM_SUPPORTED=1 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__ -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../third_party/libexif -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/interfaces -I../../drivers/peripheral/camera/vdi_base/v4l2/include -I../../drivers/peripheral/camera/vdi_base/common/dump/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1/simd/arm -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1 -I../../drivers/peripheral/camera/vdi_base/common/buffer_manager/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/metadata_manager/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/host_stream/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/utils -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sensor_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/node_base -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sink_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/source_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/merge_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/dummy_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/builder -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/dispatcher -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/parser -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy/config -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../drivers/peripheral/camera/vdi_base/common/utils/event -I../../drivers/peripheral/camera/vdi_base/common/utils/exif -I../../drivers/peripheral/camera/vdi_base/common/utils/watchdog -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../foundation/graphic/graphic_surface/utils/frame_report/export -I../../foundation/graphic/graphic_surface/surface/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api -I../../foundation/graphic/graphic_surface/interfaces/inner_api/surface -I../../foundation/graphic/graphic_surface/interfaces/inner_api/common -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../foundation/graphic/graphic_surface/sandbox -I../../foundation/graphic/graphic_surface/scoped_bytrace/include -I../../foundation/graphic/graphic_surface/utils/rs_frame_report_ext/include -I../../drivers/hdf_core/interfaces/inner_api/host/shared -I../../drivers/hdf_core/interfaces/inner_api/host/uhdf -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DNEON_INTRINSICS -Wno-pointer-sign -mfpu=neon -mfloat-abi=softfp -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -Wno-error=deprecated-declarations -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_codec_node.cpp -o obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_codec_node.o"
WORKING_DIR = '/home/<USER>/ohos_5.0/openharmony-5.0.0/out/rk3568'

# --- 2. 最小化测试逻辑 ---
def run_minimal_test():
    if not os.path.exists(LIBCLANG_PATH):
        print(f"错误: 找不到 libclang 库 -> {LIBCLANG_PATH}")
        return
    clang.cindex.Config.set_library_file(LIBCLANG_PATH)
    print(f"使用的 libclang: {LIBCLANG_PATH}")

    args = shlex.split(COMMAND_STR)
    
    # 移除 ccache
    if 'ccache' in os.path.basename(args[0]):
        args = args[1:]
    
    compiler_path_relative = args[0]
    args_for_clang = args[1:]
    
    original_cwd = os.getcwd()
    original_path_env = os.environ.get('PATH', '')
    
    try:
        print(f"切换工作目录到: {WORKING_DIR}")
        os.chdir(WORKING_DIR)

        # --- 核心修正：将编译器路径添加到 PATH 环境变量 ---
        compiler_path_absolute = os.path.abspath(compiler_path_relative)
        compiler_dir = os.path.dirname(compiler_path_absolute)
        
        print(f"将编译器目录添加到 PATH: {compiler_dir}")
        os.environ['PATH'] = f"{compiler_dir}:{original_path_env}"
        # --- 修正结束 ---

        print("开始解析...")
        index = clang.cindex.Index.create()
        # 解析时，第一个参数必须是我们要分析的文件的真实路径
        # 由于我们已经 chdir，所以 FILE_TO_PARSE 必须是绝对路径
        tu = index.parse(FILE_TO_PARSE, args=args_for_clang)

        print("解析完成。检查诊断信息...")
        diags = list(tu.diagnostics)
        if diags:
            print(f"发现 {len(diags)} 条诊断信息:")
            for diag in diags:
                print(f"  - 等级: {diag.severity}, 位置: {diag.location}, 内容: {diag.spelling}")
        else:
            print("\n" + "*"*20)
            print("恭喜！文件解析成功，没有任何错误或警告！")
            print("这意味着所有环境问题均已解决。")
            print("*"*20)

    except clang.cindex.TranslationUnitLoadError as e:
        print(f"\n捕获到致命解析错误: {e}")
    except Exception as e:
        print(f"\n捕获到未知异常: {e}")
    finally:
        # 恢复环境变量和工作目录
        os.environ['PATH'] = original_path_env
        os.chdir(original_cwd)

if __name__ == '__main__':
    run_minimal_test()