import clang.cindex
import json
import os
import shlex

# --- 全局配置 ---
PROJECT_ROOT = '/home/<USER>/openharmony-5.0.0'
COMPARE_FILE = 'device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_face_node.cpp'

BOARDS_INFO = {
    "rk3568": {
        "host_db_path": 'compile_commands.rk3568.filtered.json',
        "board_path_prefix": "device/board/hihope/rk3568",
        "target_prefixes": [
            "device/board/hihope/rk3568/",
            "vendor/hihope/",
            "device/soc/rockchip/rk3568/",
        ]
    }
}

def load_and_filter_db(host_db_path, target_prefixes):
    """加载并过滤编译数据库"""
    print(f"正在从 {host_db_path} 加载并过滤数据...")
    try:
        with open(host_db_path, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {host_db_path}")
        return None

    filtered_commands = {}
    print("开始筛选...")
    for command in data:
        relative_file_path = command.get('file')
        compile_directory = command.get('directory')

        if not relative_file_path or not compile_directory:
            continue
        
        absolute_file_path = os.path.abspath(os.path.join(compile_directory, relative_file_path))

        if any(prefix in absolute_file_path for prefix in target_prefixes):
            filtered_commands[absolute_file_path] = command
            
    print(f"筛选完成，找到 {len(filtered_commands)} 条相关命令")
    return filtered_commands

def print_ast_tree(cursor, depth=0, max_depth=10):
    """递归打印AST树形结构"""
    if depth > max_depth:
        return
    
    # 构建缩进
    indent = ""
    if depth > 0:
        indent = "  " * (depth - 1) + "+--"
    
    # 获取节点信息
    node_kind = cursor.kind.name
    node_name = cursor.spelling if cursor.spelling else ""
    
    # 打印当前节点
    if node_name:
        print(f"{indent}{node_kind} {node_name}")
    else:
        print(f"{indent}{node_kind}")
    
    # 递归打印子节点
    for child in cursor.get_children():
        print_ast_tree(child, depth + 1, max_depth)

def parse_and_print_ast(file_path, command_entry, max_depth=8):
    """解析文件并打印AST树"""
    if not command_entry:
        print(f"警告: 找不到文件 {file_path} 的编译命令")
        return False

    original_cwd = os.getcwd()
    try:
        working_dir = command_entry['directory']
        args = shlex.split(command_entry['command'])
        
        print(f"工作目录: {working_dir}")
        os.chdir(working_dir)

        if 'ccache' in os.path.basename(args[0]):
            args = args[1:]
        
        args_for_clang = args[1:]
        
        # 创建简化的参数列表
        simple_args = []
        for arg in args_for_clang:
            if arg.startswith('-I'):
                simple_args.append(arg)
            elif arg.startswith('-D') and not any(skip in arg for skip in ['__DATE__', '__TIME__', '__TIMESTAMP__']):
                simple_args.append(arg)
            elif arg in ['-std=c++17', '-std=c++14', '-std=c++11']:
                simple_args.append(arg)
        
        simple_args.extend([
            '-std=c++17',
            '-x', 'c++',
            '-w',  # 禁用所有警告
            '-Wno-error',
            '-fsyntax-only',
            '-I/usr/include',
            '-isystem', '/usr/include/c++/9',
            '-isystem', '/usr/include/x86_64-linux-gnu/c++/9',
        ])
        
        print(f"简化参数数量: {len(simple_args)}")

        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 {file_path}")
            return False

        print(f"开始解析AST...")

        index = clang.cindex.Index.create()
        parse_options = (
            clang.cindex.TranslationUnit.PARSE_DETAILED_PROCESSING_RECORD |
            clang.cindex.TranslationUnit.PARSE_INCOMPLETE
        )
        
        tu = index.parse(file_path, args=simple_args, options=parse_options)

        if not tu:
            print(f"错误: 无法创建翻译单元")
            return False

        print(f"翻译单元创建成功")
        
        # 显示诊断信息（简化版）
        error_count = 0
        for diag in tu.diagnostics:
            if diag.severity >= 3:  # Error level
                error_count += 1
        
        if error_count > 0:
            print(f"发现 {error_count} 个错误，但继续显示AST...")

        # 获取文件名用于显示
        filename = os.path.basename(file_path)
        
        print("\n" + "="*60)
        print(f"AST 树形结构 - {filename}")
        print("="*60)
        
        # 打印AST树
        print(f"TRANSLATION_UNIT {filename}")
        for child in tu.cursor.get_children():
            # 只显示来自目标文件的节点
            if child.location.file and child.location.file.name == file_path:
                print_ast_tree(child, 1, max_depth)
        
        return True
        
    except Exception as e:
        print(f"处理文件时发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        os.chdir(original_cwd)

def main():
    board_name = "rk3568"
    
    db = load_and_filter_db(BOARDS_INFO[board_name]['host_db_path'], BOARDS_INFO[board_name]['target_prefixes'])
    
    if not db:
        print("数据库加载失败，无法继续")
        return

    file_to_analyze = os.path.join(PROJECT_ROOT, COMPARE_FILE)
    
    print("\n" + "="*60)
    print("                 C++ AST 树形结构打印工具")
    print("="*60)
    print(f"分析文件: {file_to_analyze}")
    
    success = parse_and_print_ast(file_to_analyze, db.get(file_to_analyze))
    
    if success:
        print(f"\n✅ AST树形结构显示完成！")
    else:
        print(f"\n❌ 无法解析文件的AST结构")
        print("💡 可能的原因:")
        print("   - 编译命令不正确")
        print("   - 缺少必要的头文件")
        print("   - 文件路径错误")

if __name__ == '__main__':
    try:
        import clang.cindex
        main()
    except ImportError:
        print("错误: 找不到 clang python 库。请运行 'python3 -m pip install libclang'")
        exit()
