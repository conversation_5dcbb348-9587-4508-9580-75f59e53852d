# Device Code Generator

This tool is to generate device source code from common templates, this enables developers to quickly adapt OpenHarmony to new hardware platforms.

TODO:

1. Analyze the source code of different devices and identify discrepancies.
```
--- template/soc/rk3399/hardware/display/src/display_device/drm_connector.cpp   2025-04-25 13:48:29.324788938 +0800
+++ template/soc/rk3568/hardware/display/src/display_device/drm_connector.cpp   2025-04-25 13:48:29.352788894 +0800
-const int BACKLIGHT_MIN = 10;
 int32_t DrmConnector::SetBrightness(uint32_t level)
 {
     static int32_t brFd = 0;
     const int32_t buffer_size = 10; /* buffer size */
     char buffer[buffer_size];

-    DISPLAY_DEBUGLOG("set %{public}d", level);
+    DISPLAY_LOGD("set %{public}d", level);
     if (mPropBrightnessId == DRM_INVALID_ID) {
         DISPLAY_LOGE("the prop id of brightness is invalid");
         return DISPLAY_NOT_SUPPORT;
@@ -137,9 +137,6 @@
         DISPLAY_LOGE("memset_s failed\n");
         return DISPLAY_FAILURE;
     }
-    if (level < BACKLIGHT_MIN) {
-        level = BACKLIGHT_MIN;
-    }
     int bytes = sprintf_s(buffer, sizeof(buffer), "%d\n", level);
     if (bytes < 0) {
         DISPLAY_LOGE("change failed\n");
@@ -155,12 +152,7 @@
     cap.phyHeight = mPhyHeight;
     cap.phyWidth = mPhyWidth;
     cap.type = mType;
-    memcpy_s(cap.name, sizeof(cap.name), mName.c_str(), mName.size());
-    if (mName.size() >= sizeof(cap.name)) {
-        cap.name[sizeof(cap.name) - 1] = 0;
-    } else {
-        cap.name[mName.size()] = 0;
-    }
+    cap.name = mName;
     cap.supportLayers = mSupportLayers;
     cap.virtualDispCount = mVirtualDispCount;
     cap.supportWriteBack = mSupportWriteBack;
```

2. Using Python to generate the C/C++ code with Jinja2/libclang/...  e.g.
   * https://markvtechblog.wordpress.com/2024/04/28/code-generation-in-python-with-jinja2/
   * https://szelei.me/code-generator/

3. Organize the configuration files and add duplicate content to the template.
