[{"file": "../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/src/rkispv5.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/src/camera_device_manager/rkispv5.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../base/hiviewdfx/interfaces/innerkits/libhilog/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../third_party/bounds_checking_function/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\"  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/src/rkispv5.cpp -o  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/src/camera_device_manager/rkispv5.o"}, {"file": "../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/ipp_algo_example/ipp_algo_example.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/ipp_algo_example/camera_ipp_algo_example/ipp_algo_example.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2  -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -Wno-error -Wno-unused-function -Wno-unused-parameter  --sysroot=obj/third_party/musl -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/ipp_algo_example/ipp_algo_example.c -o  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/ipp_algo_example/camera_ipp_algo_example/ipp_algo_example.o"}, {"file": "../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_codec_node.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_codec_node.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -DHAVE_UNSIGNED_SHORT=1 -DNEON_INTRINSICS -DC_ARITH_CODING_SUPPORTED=1 -DD_ARITH_CODING_SUPPORTED=1 -DBMP_SUPPORTED=1 -DPPM_SUPPORTED=1 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../third_party/libexif -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/interfaces -I../../drivers/peripheral/camera/vdi_base/v4l2/include -I../../drivers/peripheral/camera/vdi_base/common/dump/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1/simd/arm -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1 -I../../drivers/peripheral/camera/vdi_base/common/buffer_manager/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/metadata_manager/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/host_stream/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/utils -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sensor_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/node_base -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sink_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/source_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/merge_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/dummy_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/builder -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/dispatcher -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/parser -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy/config -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../drivers/peripheral/camera/vdi_base/common/utils/event -I../../drivers/peripheral/camera/vdi_base/common/utils/exif -I../../drivers/peripheral/camera/vdi_base/common/utils/watchdog -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../foundation/graphic/graphic_surface/utils/frame_report/export -I../../foundation/graphic/graphic_surface/surface/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api -I../../foundation/graphic/graphic_surface/interfaces/inner_api/surface -I../../foundation/graphic/graphic_surface/interfaces/inner_api/common -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../foundation/graphic/graphic_surface/sandbox -I../../foundation/graphic/graphic_surface/scoped_bytrace/include -I../../foundation/graphic/graphic_surface/utils/rs_frame_report_ext/include -I../../drivers/hdf_core/interfaces/inner_api/host/shared -I../../drivers/hdf_core/interfaces/inner_api/host/uhdf -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DNEON_INTRINSICS -Wno-pointer-sign -mfpu=neon -mfloat-abi=softfp -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -Wno-error=deprecated-declarations -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_codec_node.cpp -o  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_codec_node.o"}, {"file": "../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_exif_node.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_exif_node.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -DHAVE_UNSIGNED_SHORT=1 -DNEON_INTRINSICS -DC_ARITH_CODING_SUPPORTED=1 -DD_ARITH_CODING_SUPPORTED=1 -DBMP_SUPPORTED=1 -DPPM_SUPPORTED=1 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../third_party/libexif -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/interfaces -I../../drivers/peripheral/camera/vdi_base/v4l2/include -I../../drivers/peripheral/camera/vdi_base/common/dump/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1/simd/arm -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1 -I../../drivers/peripheral/camera/vdi_base/common/buffer_manager/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/metadata_manager/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/host_stream/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/utils -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sensor_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/node_base -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sink_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/source_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/merge_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/dummy_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/builder -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/dispatcher -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/parser -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy/config -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../drivers/peripheral/camera/vdi_base/common/utils/event -I../../drivers/peripheral/camera/vdi_base/common/utils/exif -I../../drivers/peripheral/camera/vdi_base/common/utils/watchdog -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../foundation/graphic/graphic_surface/utils/frame_report/export -I../../foundation/graphic/graphic_surface/surface/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api -I../../foundation/graphic/graphic_surface/interfaces/inner_api/surface -I../../foundation/graphic/graphic_surface/interfaces/inner_api/common -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../foundation/graphic/graphic_surface/sandbox -I../../foundation/graphic/graphic_surface/scoped_bytrace/include -I../../foundation/graphic/graphic_surface/utils/rs_frame_report_ext/include -I../../drivers/hdf_core/interfaces/inner_api/host/shared -I../../drivers/hdf_core/interfaces/inner_api/host/uhdf -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DNEON_INTRINSICS -Wno-pointer-sign -mfpu=neon -mfloat-abi=softfp -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -Wno-error=deprecated-declarations -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_exif_node.cpp -o  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_exif_node.o"}, {"file": "../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_face_node.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_face_node.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -DHAVE_UNSIGNED_SHORT=1 -DNEON_INTRINSICS -DC_ARITH_CODING_SUPPORTED=1 -DD_ARITH_CODING_SUPPORTED=1 -DBMP_SUPPORTED=1 -DPPM_SUPPORTED=1 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../third_party/libexif -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/interfaces -I../../drivers/peripheral/camera/vdi_base/v4l2/include -I../../drivers/peripheral/camera/vdi_base/common/dump/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1/simd/arm -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1 -I../../drivers/peripheral/camera/vdi_base/common/buffer_manager/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/metadata_manager/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/host_stream/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/utils -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sensor_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/node_base -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sink_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/source_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/merge_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/dummy_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/builder -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/dispatcher -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/parser -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy/config -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../drivers/peripheral/camera/vdi_base/common/utils/event -I../../drivers/peripheral/camera/vdi_base/common/utils/exif -I../../drivers/peripheral/camera/vdi_base/common/utils/watchdog -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../foundation/graphic/graphic_surface/utils/frame_report/export -I../../foundation/graphic/graphic_surface/surface/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api -I../../foundation/graphic/graphic_surface/interfaces/inner_api/surface -I../../foundation/graphic/graphic_surface/interfaces/inner_api/common -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../foundation/graphic/graphic_surface/sandbox -I../../foundation/graphic/graphic_surface/scoped_bytrace/include -I../../foundation/graphic/graphic_surface/utils/rs_frame_report_ext/include -I../../drivers/hdf_core/interfaces/inner_api/host/shared -I../../drivers/hdf_core/interfaces/inner_api/host/uhdf -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DNEON_INTRINSICS -Wno-pointer-sign -mfpu=neon -mfloat-abi=softfp -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -Wno-error=deprecated-declarations -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_face_node.cpp -o  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_face_node.o"}, {"file": "../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_node_utils.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_node_utils.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -DHAVE_UNSIGNED_SHORT=1 -DNEON_INTRINSICS -DC_ARITH_CODING_SUPPORTED=1 -DD_ARITH_CODING_SUPPORTED=1 -DBMP_SUPPORTED=1 -DPPM_SUPPORTED=1 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../third_party/libexif -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/interfaces -I../../drivers/peripheral/camera/vdi_base/v4l2/include -I../../drivers/peripheral/camera/vdi_base/common/dump/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1/simd/arm -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1 -I../../drivers/peripheral/camera/vdi_base/common/buffer_manager/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/metadata_manager/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/host_stream/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/utils -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sensor_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/node_base -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sink_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/source_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/merge_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/dummy_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/builder -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/dispatcher -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/parser -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy/config -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../drivers/peripheral/camera/vdi_base/common/utils/event -I../../drivers/peripheral/camera/vdi_base/common/utils/exif -I../../drivers/peripheral/camera/vdi_base/common/utils/watchdog -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../foundation/graphic/graphic_surface/utils/frame_report/export -I../../foundation/graphic/graphic_surface/surface/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api -I../../foundation/graphic/graphic_surface/interfaces/inner_api/surface -I../../foundation/graphic/graphic_surface/interfaces/inner_api/common -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../foundation/graphic/graphic_surface/sandbox -I../../foundation/graphic/graphic_surface/scoped_bytrace/include -I../../foundation/graphic/graphic_surface/utils/rs_frame_report_ext/include -I../../drivers/hdf_core/interfaces/inner_api/host/shared -I../../drivers/hdf_core/interfaces/inner_api/host/uhdf -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DNEON_INTRINSICS -Wno-pointer-sign -mfpu=neon -mfloat-abi=softfp -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -Wno-error=deprecated-declarations -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_node_utils.cpp -o  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_node_utils.o"}, {"file": "../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_scale_node.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_scale_node.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -DHAVE_UNSIGNED_SHORT=1 -DNEON_INTRINSICS -DC_ARITH_CODING_SUPPORTED=1 -DD_ARITH_CODING_SUPPORTED=1 -DBMP_SUPPORTED=1 -DPPM_SUPPORTED=1 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../third_party/libexif -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/interfaces -I../../drivers/peripheral/camera/vdi_base/v4l2/include -I../../drivers/peripheral/camera/vdi_base/common/dump/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1/simd/arm -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1 -I../../drivers/peripheral/camera/vdi_base/common/buffer_manager/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/metadata_manager/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/host_stream/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/utils -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sensor_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/node_base -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sink_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/source_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/merge_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/dummy_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/builder -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/dispatcher -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/parser -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy/config -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../drivers/peripheral/camera/vdi_base/common/utils/event -I../../drivers/peripheral/camera/vdi_base/common/utils/exif -I../../drivers/peripheral/camera/vdi_base/common/utils/watchdog -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../foundation/graphic/graphic_surface/utils/frame_report/export -I../../foundation/graphic/graphic_surface/surface/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api -I../../foundation/graphic/graphic_surface/interfaces/inner_api/surface -I../../foundation/graphic/graphic_surface/interfaces/inner_api/common -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../foundation/graphic/graphic_surface/sandbox -I../../foundation/graphic/graphic_surface/scoped_bytrace/include -I../../foundation/graphic/graphic_surface/utils/rs_frame_report_ext/include -I../../drivers/hdf_core/interfaces/inner_api/host/shared -I../../drivers/hdf_core/interfaces/inner_api/host/uhdf -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DNEON_INTRINSICS -Wno-pointer-sign -mfpu=neon -mfloat-abi=softfp -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -Wno-error=deprecated-declarations -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -Wall -Wextra -Werror -Wno-error -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -fno-strict-aliasing -Wno-sign-compare -Wno-builtin-requires-header -Wno-unused-variable -Wno-unused-label -Wno-implicit-function-declaration -Wno-format -Wno-int-conversion -Wno-unused-function -Wno-thread-safety-attributes -Wno-inconsistent-missing-override -fno-rtti -fno-exceptions -ffunction-sections -fdata-sections -c ../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/rk_scale_node.cpp -o  obj/device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node/camera_pipeline_core/rk_scale_node.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/src/mpi_enc_utils.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/src/camera_pipeline_core/mpi_enc_utils.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DSUPPORT_V4L2 -DHAVE_UNSIGNED_SHORT=1 -DNEON_INTRINSICS -DC_ARITH_CODING_SUPPORTED=1 -DD_ARITH_CODING_SUPPORTED=1 -DBMP_SUPPORTED=1 -DPPM_SUPPORTED=1 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/device_manager/include -I../../commonlibrary/c_utils/base/include -I../../device/board/hihope/rk3568/camera/vdi_impl/v4l2/pipeline_core/src/node -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../third_party/libexif -I../../drivers/peripheral/camera/vdi_base/common/include -I../../drivers/peripheral/camera/vdi_base/interfaces -I../../drivers/peripheral/camera/vdi_base/v4l2/include -I../../drivers/peripheral/camera/vdi_base/common/dump/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../drivers/interface/camera/metadata/include -I../../drivers/interface/camera/sequenceable/buffer_producer -I../../drivers/peripheral/base -I../../drivers/interface/camera/sequenceable/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../base/notification/eventhandler/interfaces/inner_api -I../../base/notification/eventhandler/frameworks/eventhandler/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1/simd/arm -Igen/third_party/libjpeg-turbo/libjpeg-turbo-2.1.1 -I../../drivers/peripheral/camera/vdi_base/common/buffer_manager/include -I../../drivers/peripheral/camera/vdi_base/common/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/device_manager/include -I../../drivers/peripheral/camera/vdi_base/common/metadata_manager/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/host_stream/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/utils -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sensor_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/node_base -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/sink_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/source_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/merge_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/nodes/src/dummy_node -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/include -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/builder -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/dispatcher -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/parser -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/pipeline_impl/src/strategy/config -I../../drivers/peripheral/camera/vdi_base/common/pipeline_core/ipp/include -I../../drivers/peripheral/camera/vdi_base/common/utils/event -I../../drivers/peripheral/camera/vdi_base/common/utils/exif -I../../drivers/peripheral/camera/vdi_base/common/utils/watchdog -I../../drivers/peripheral/camera/vdi_base/common/adapter/platform/v4l2/src/driver_adapter/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../foundation/graphic/graphic_surface/utils/frame_report/export -I../../foundation/graphic/graphic_surface/surface/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api -I../../foundation/graphic/graphic_surface/interfaces/inner_api/surface -I../../foundation/graphic/graphic_surface/interfaces/inner_api/common -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../foundation/graphic/graphic_surface/sandbox -I../../foundation/graphic/graphic_surface/scoped_bytrace/include -I../../foundation/graphic/graphic_surface/utils/rs_frame_report_ext/include -I../../drivers/hdf_core/interfaces/inner_api/host/shared -I../../drivers/hdf_core/interfaces/inner_api/host/uhdf -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DNEON_INTRINSICS -Wno-pointer-sign -mfpu=neon -mfloat-abi=softfp -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -DGST_DISABLE_DEPRECATED -DHAVE_CONFIG_H -DCOLORSPACE=\"videoconvert\" -Wno-error=deprecated-declarations -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/mpp/src/mpi_enc_utils.c -o  obj/device/soc/rockchip/rk3568/hardware/mpp/src/camera_pipeline_core/mpi_enc_utils.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../drivers/peripheral/codec/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../third_party/bounds_checking_function/include -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/display/hdi_service/gralloc/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -Wno-format -Wno-unused-parameter -Wno-unused-function -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-int-conversion -Wno-macro-redefined --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp.c -o  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_component_manager.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp_component_manager.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../drivers/peripheral/codec/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../third_party/bounds_checking_function/include -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/display/hdi_service/gralloc/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_component_manager.cpp -o  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp_component_manager.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_config.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp_config.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../drivers/peripheral/codec/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../third_party/bounds_checking_function/include -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/display/hdi_service/gralloc/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -Wno-format -Wno-unused-parameter -Wno-unused-function -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-int-conversion -Wno-macro-redefined --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_config.c -o  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp_config.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_mpi.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp_mpi.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../drivers/peripheral/codec/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../third_party/bounds_checking_function/include -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/display/hdi_service/gralloc/include -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -Wno-format -Wno-unused-parameter -Wno-unused-function -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-int-conversion -Wno-macro-redefined --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_mpi.c -o  obj/device/soc/rockchip/rk3568/hardware/codec/src/libcodec_oem_interface/hdi_mpp_mpi.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_mpi.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/src/libjpeg_vdi_impl/hdi_mpp_mpi.o.d  -DLOG_TAG_IMAGE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/codec/jpeg/include -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/peripheral/codec/image/vdi -I../../drivers/peripheral/codec/utils/include -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/hdf_core/framework/include/utils -I../../drivers/hdf_core/adapter/uhdf2/osal/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/codec/src/hdi_mpp_mpi.c -o  obj/device/soc/rockchip/rk3568/hardware/codec/src/libjpeg_vdi_impl/hdi_mpp_mpi.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_decoder.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_decoder.o.d  -DLOG_TAG_IMAGE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/codec/jpeg/include -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/peripheral/codec/image/vdi -I../../drivers/peripheral/codec/utils/include -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/hdf_core/framework/include/utils -I../../drivers/hdf_core/adapter/uhdf2/osal/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_decoder.cpp -o  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_decoder.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_helper.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_helper.o.d  -DLOG_TAG_IMAGE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/codec/jpeg/include -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/peripheral/codec/image/vdi -I../../drivers/peripheral/codec/utils/include -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/hdf_core/framework/include/utils -I../../drivers/hdf_core/adapter/uhdf2/osal/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_helper.cpp -o  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_helper.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_impl.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_impl.o.d  -DLOG_TAG_IMAGE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/codec/jpeg/include -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/peripheral/codec/image/vdi -I../../drivers/peripheral/codec/utils/include -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/hdf_core/framework/include/utils -I../../drivers/hdf_core/adapter/uhdf2/osal/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_impl.cpp -o  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_impl.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_interface.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_interface.o.d  -DLOG_TAG_IMAGE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/codec/jpeg/include -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/peripheral/codec/image/vdi -I../../drivers/peripheral/codec/utils/include -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/interfaces/include -I../../device/soc/rockchip/rk3568/hardware/codec/include -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/hdf_core/framework/include/utils -I../../drivers/hdf_core/adapter/uhdf2/osal/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/codec/jpeg/src/codec_jpeg_interface.cpp -o  obj/device/soc/rockchip/rk3568/hardware/codec/jpeg/src/libjpeg_vdi_impl/codec_jpeg_interface.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_connector.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_connector.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_connector.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_connector.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_crtc.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_crtc.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_crtc.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_crtc.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_device.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_device.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_device.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_device.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_display.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_display.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_display.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_display.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_encoder.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_encoder.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_encoder.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_encoder.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_plane.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_plane.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_plane.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_plane.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_vsync_worker.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_vsync_worker.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/drm_vsync_worker.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/drm_vsync_worker.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_composer.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_composer.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_composer.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_composer.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_device_interface.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_device_interface.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_device_interface.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_device_interface.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_display.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_display.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_display.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_display.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_drm_composition.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_drm_composition.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_drm_composition.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_drm_composition.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_drm_layer.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_drm_layer.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_drm_layer.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_drm_layer.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_gfx_composition.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_gfx_composition.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_gfx_composition.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_gfx_composition.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_layer.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_layer.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_layer.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_layer.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_netlink_monitor.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_netlink_monitor.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_netlink_monitor.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_netlink_monitor.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_session.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_session.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/buffer/hdi_service/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../drivers/interface/display/composer -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/hiviewdfx/hitrace/interfaces/native/innerkits/include/hitrace_meter  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-error=unused-function -Wno-error=missing-braces -Wno-error=#warnings -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/hdi_session.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vendor/hdi_session.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_gfx/display_gfx.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gfx/display_gfx/display_gfx.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../base/hiviewdfx/interfaces/innerkits/libhilog/include -I../../commonlibrary/c_utils/base/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/base -I../../foundation/graphic/standard/utils/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-macro-redefined -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_gfx/display_gfx.c -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gfx/display_gfx/display_gfx.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/display_buffer_vdi_impl.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/libdisplay_buffer_vdi_impl/display_buffer_vdi_impl.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../drivers/peripheral/base -I../../drivers/interface/display/composer/hdifd_parcelable -I../../drivers/interface/display/buffer -I../../drivers/peripheral/display/utils/include -I../../drivers/peripheral/display/buffer/hdi_service/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -DGRALLOC_GBM_SUPPORT -Wno-macro-redefined -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/display_buffer_vdi_impl.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/libdisplay_buffer_vdi_impl/display_buffer_vdi_impl.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/display_gralloc_gbm.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/libdisplay_buffer_vendor/display_gralloc_gbm.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/display/include -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../drivers/peripheral/base -I../../drivers/interface/display/composer/hdifd_parcelable -I../../drivers/peripheral/display/utils/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/buffer -Igen/drivers/interface/display/composer -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -DGRALLOC_GBM_SUPPORT -Wno-macro-redefined -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/display_gralloc_gbm.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/libdisplay_buffer_vendor/display_gralloc_gbm.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vdi_impl.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/libdisplay_composer_vdi_impl/display_composer_vdi_impl.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_device -I../../drivers/peripheral/display/utils/include -I../../drivers/interface/display/composer -I../../drivers/peripheral/display/composer/hdi_service/include -I../../drivers/interface/display/composer/hdifd_parcelable -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hisysevent/interfaces/native/innerkits/hisysevent/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/samgr_proxy/include -I../../foundation/systemabilitymgr/samgr/interfaces/innerkits/dynamic_cache/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/adapter/uhdf2/ipc/include -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/hdi -Igen/drivers/interface -Igen/drivers/interface/display/composer -I../../foundation/graphic/graphic_surface/interfaces/inner_api/utils -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -I../../foundation/communication/ipc/ipc/native/src/core/include -I../../foundation/communication/ipc/ipc/native/src/mock/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -DCONFIG_STANDARD_SYSTEM -DBUILD_PUBLIC_VERSION -DCONFIG_ACTV_BINDER  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_device/display_composer_vdi_impl.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_device/libdisplay_composer_vdi_impl/display_composer_vdi_impl.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/hi_gbm.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/libhigbm_vendor/hi_gbm.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc -I../../drivers/peripheral/display/utils/include -I../../foundation/communication/ipc/interfaces/innerkits/ipc_core/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/libdrm -I../../third_party/libdrm/include -I../../third_party/libdrm/include/drm -I../../third_party/bounds_checking_function/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -DGRALLOC_GBM_SUPPORT -Wno-macro-redefined -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/hi_gbm.cpp -o  obj/device/soc/rockchip/rk3568/hardware/display/src/display_gralloc/libhigbm_vendor/hi_gbm.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp/hdi_mpp_mpi.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp/hdi_mpp/hdi_mpp_mpi.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../drivers/framework/include/utils -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi  -Wno-format -Wno-unused-parameter -Wno-unused-function -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-int-conversion -Wno-macro-redefined -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp/hdi_mpp_mpi.cpp -o  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp/hdi_mpp/hdi_mpp_mpi.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rk_list.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/rk_list.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/inc -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wall -Wextra -Werror -Wno-predefined-identifier-outside-function -Wno-macro-redefined -Wno-format-extra-args -Wno-format -DHAVE_CONFIG_H -DMPP_VERSION=\"1.3.7\" -DMPP_VER_HIST_CNT=0 -DMPP_VER_HIST_0=\"version_0\" -DMPP_VER_HIST_1=\"version_1\" -DMPP_VER_HIST_2=\"version_2\" -DMPP_VER_HIST_3=\"version_3\" -DMPP_VER_HIST_4=\"version_4\" -DMPP_VER_HIST_5=\"version_5\" -DMPP_VER_HIST_6=\"version_6\" -DMPP_VER_HIST_7=\"version_7\" -DMPP_VER_HIST_8=\"version_8\" -DMPP_VER_HIST_9=\"version_9\" -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rk_list.cpp -o  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/rk_list.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/inc -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wall -Wextra -Werror -Wno-format --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu.c -o  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_api.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_api.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/inc -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wall -Wextra -Werror -Wno-predefined-identifier-outside-function -Wno-macro-redefined -Wno-format-extra-args -Wno-format -DHAVE_CONFIG_H -DMPP_VERSION=\"1.3.7\" -DMPP_VER_HIST_CNT=0 -DMPP_VER_HIST_0=\"version_0\" -DMPP_VER_HIST_1=\"version_1\" -DMPP_VER_HIST_2=\"version_2\" -DMPP_VER_HIST_3=\"version_3\" -DMPP_VER_HIST_4=\"version_4\" -DMPP_VER_HIST_5=\"version_5\" -DMPP_VER_HIST_6=\"version_6\" -DMPP_VER_HIST_7=\"version_7\" -DMPP_VER_HIST_8=\"version_8\" -DMPP_VER_HIST_9=\"version_9\" -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_api.cpp -o  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_api.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_api_legacy.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_api_legacy.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/inc -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wall -Wextra -Werror -Wno-predefined-identifier-outside-function -Wno-macro-redefined -Wno-format-extra-args -Wno-format -DHAVE_CONFIG_H -DMPP_VERSION=\"1.3.7\" -DMPP_VER_HIST_CNT=0 -DMPP_VER_HIST_0=\"version_0\" -DMPP_VER_HIST_1=\"version_1\" -DMPP_VER_HIST_2=\"version_2\" -DMPP_VER_HIST_3=\"version_3\" -DMPP_VER_HIST_4=\"version_4\" -DMPP_VER_HIST_5=\"version_5\" -DMPP_VER_HIST_6=\"version_6\" -DMPP_VER_HIST_7=\"version_7\" -DMPP_VER_HIST_8=\"version_8\" -DMPP_VER_HIST_9=\"version_9\" -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_api_legacy.cpp -o  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_api_legacy.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_api_mlvec.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_api_mlvec.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/inc -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wall -Wextra -Werror -Wno-predefined-identifier-outside-function -Wno-macro-redefined -Wno-format-extra-args -Wno-format -DHAVE_CONFIG_H -DMPP_VERSION=\"1.3.7\" -DMPP_VER_HIST_CNT=0 -DMPP_VER_HIST_0=\"version_0\" -DMPP_VER_HIST_1=\"version_1\" -DMPP_VER_HIST_2=\"version_2\" -DMPP_VER_HIST_3=\"version_3\" -DMPP_VER_HIST_4=\"version_4\" -DMPP_VER_HIST_5=\"version_5\" -DMPP_VER_HIST_6=\"version_6\" -DMPP_VER_HIST_7=\"version_7\" -DMPP_VER_HIST_8=\"version_8\" -DMPP_VER_HIST_9=\"version_9\" -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_api_mlvec.cpp -o  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_api_mlvec.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_mem_legacy.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_mem_legacy.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/inc -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wall -Wextra -Werror -Wno-format --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/vpu_mem_legacy.c -o  obj/device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy/rockchip_vpu_src/vpu_mem_legacy.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/common/Rockchip_OMX_Basecomponent.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/common/RkOMX_Basecomponent/Rockchip_OMX_Basecomponent.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include  -Wno-implicit-function-declaration -Wno-format -Wno-pointer-to-int-cast -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/common/Rockchip_OMX_Basecomponent.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/common/RkOMX_Basecomponent/Rockchip_OMX_Basecomponent.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/common/Rockchip_OMX_Baseport.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/common/RkOMX_Basecomponent/Rockchip_OMX_Baseport.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include  -Wno-implicit-function-declaration -Wno-format -Wno-pointer-to-int-cast -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/common/Rockchip_OMX_Baseport.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/common/RkOMX_Basecomponent/Rockchip_OMX_Baseport.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/common/Rockchip_OMX_Resourcemanager.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/common/libRkOMX_Resourcemanager/Rockchip_OMX_Resourcemanager.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-implicit-function-declaration -DSUPPORT_RGA -Wno-pointer-to-int-cast -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/common/Rockchip_OMX_Resourcemanager.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/common/libRkOMX_Resourcemanager/Rockchip_OMX_Resourcemanager.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/Rkvpu_OMX_Vdec.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/libomxvpu_dec/Rkvpu_OMX_Vdec.o.d  -DAVS100 -DUSE_DRM -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DSUPPORT_HEVC -DSUPPORT_VP9 -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/hihope/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/codec/interfaces/include -I../../drivers/peripheral/display/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-unused-variable -Wno-implicit-function-declaration -Wno-switch -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/Rkvpu_OMX_Vdec.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/libomxvpu_dec/Rkvpu_OMX_Vdec.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/Rkvpu_OMX_VdecControl.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/libomxvpu_dec/Rkvpu_OMX_VdecControl.o.d  -DAVS100 -DUSE_DRM -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DSUPPORT_HEVC -DSUPPORT_VP9 -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/hihope/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/codec/interfaces/include -I../../drivers/peripheral/display/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-unused-variable -Wno-implicit-function-declaration -Wno-switch -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/Rkvpu_OMX_VdecControl.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/libomxvpu_dec/Rkvpu_OMX_VdecControl.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/library_register.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/libomxvpu_dec/library_register.o.d  -DAVS100 -DUSE_DRM -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DSUPPORT_HEVC -DSUPPORT_VP9 -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/hihope/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/codec/interfaces/include -I../../drivers/peripheral/display/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-unused-variable -Wno-implicit-function-declaration -Wno-switch -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/library_register.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec/libomxvpu_dec/library_register.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/Rkvpu_OMX_Venc.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/libomxvpu_enc/Rkvpu_OMX_Venc.o.d  -DAVS100 -DUSE_DRM -DHAVE_L1_SVP_MODE -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DSUPPORT_HEVC_ENC -DSUPPORT_VP8_ENC -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/hihope/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/codec/interfaces/include -I../../drivers/peripheral/display/interfaces/include -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-unused-variable -Wno-implicit-function-declaration -Wno-format -Wno-switch -Wno-pointer-to-int-cast -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/Rkvpu_OMX_Venc.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/libomxvpu_enc/Rkvpu_OMX_Venc.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/Rkvpu_OMX_VencControl.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/libomxvpu_enc/Rkvpu_OMX_VencControl.o.d  -DAVS100 -DUSE_DRM -DHAVE_L1_SVP_MODE -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DSUPPORT_HEVC_ENC -DSUPPORT_VP8_ENC -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/hihope/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/codec/interfaces/include -I../../drivers/peripheral/display/interfaces/include -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-unused-variable -Wno-implicit-function-declaration -Wno-format -Wno-switch -Wno-pointer-to-int-cast -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/Rkvpu_OMX_VencControl.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/libomxvpu_enc/Rkvpu_OMX_VencControl.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/library_register.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/libomxvpu_enc/library_register.o.d  -DAVS100 -DUSE_DRM -DHAVE_L1_SVP_MODE -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DSUPPORT_HEVC_ENC -DSUPPORT_VP8_ENC -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/hihope/rk3568/hardware/rga/include -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/codec/interfaces/include -I../../drivers/peripheral/display/interfaces/include -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wno-unused-variable -Wno-implicit-function-declaration -Wno-format -Wno-switch -Wno-pointer-to-int-cast -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/library_register.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc/libomxvpu_enc/library_register.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/core/Rockchip_OMX_Component_Register.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/core/libOMX_Core/Rockchip_OMX_Component_Register.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wall -Wextra -Werror -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/core/Rockchip_OMX_Component_Register.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/core/libOMX_Core/Rockchip_OMX_Component_Register.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/core/Rockchip_OMX_Core.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/core/libOMX_Core/Rockchip_OMX_Core.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/core -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -Wall -Wextra -Werror -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/core/Rockchip_OMX_Core.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/core/libOMX_Core/Rockchip_OMX_Core.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/libOMXPlugin/OMXPlugin.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/libOMXPlugin/libOMX_Pluginhw/OMXPlugin.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../device/soc/rockchip/rk3568/hardware/omx_il/libOMXPlugin -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../foundation/multimedia/player_framework/services/utils/include -I../../third_party/openmax/api/1.1.2 -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include  -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wall -Wextra -Werror -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/omx_il/libOMXPlugin/OMXPlugin.cpp -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/libOMXPlugin/libOMX_Pluginhw/OMXPlugin.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_ColorUtils.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_ColorUtils.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-implicit-function-declaration -Wno-unused-variable -Wno-format -Wno-switch -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_ColorUtils.cpp -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_ColorUtils.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_ETC.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_ETC.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_ETC.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_ETC.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Env.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Env.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Env.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Env.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Event.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Event.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Event.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Event.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Library.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Library.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Library.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Library.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Log.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Log.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Log.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Log.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Memory.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Memory.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Memory.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Memory.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Mutex.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Mutex.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Mutex.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Mutex.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_OHOS.cpp", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang++ -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_OHOS.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  -Wno-implicit-function-declaration -Wno-unused-variable -Wno-format -Wno-switch -std=c++17 -fno-exceptions -fno-rtti --sysroot=obj/third_party/musl -fvisibility-inlines-hidden -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_OHOS.cpp -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_OHOS.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Queue.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Queue.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Queue.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Queue.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_RGA_Process.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_RGA_Process.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_RGA_Process.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_RGA_Process.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Semaphore.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Semaphore.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Semaphore.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Semaphore.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_SharedMemory.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_SharedMemory.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_SharedMemory.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_SharedMemory.o"}, {"file": "../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Thread.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Thread.o.d  -DAVS100 -DOHOS -DSUPPORT_AFBC -DROCKCHIP_GPU_LIB_ENABLE -DSUPPORT_RGA -DAVS80 -DOHOS_BUFFER_HANDLE -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -D__OHOS_USER__ -D__OHOS_STANDARD_SYS__  -I../../device/soc/rockchip/rk3568/hardware/omx_il/osal -I../../device/soc/rockchip/rk3568/hardware/mpp/mpp/legacy -I../../device/soc/rockchip/rk3568/hardware/mpp/include -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/common -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/dec -I../../device/soc/rockchip/rk3568/hardware/omx_il/component/video/enc -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/khronos -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/rockchip -I../../device/soc/rockchip/rk3568/hardware/omx_il/include/librkvpu -I../../device/soc/rockchip/rk3568/hardware/rga/include -I../../drivers/peripheral/base -I../../drivers/peripheral/display/interfaces/include -I../../drivers/peripheral/codec/interfaces/include -I../../third_party/libdrm/include/drm -I../../third_party/openmax/api/1.1.2 -I../../commonlibrary/c_utils/base/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../third_party/bounds_checking_function/include -I../../drivers/hdf_core/interfaces/inner_api/osal/shared -I../../drivers/hdf_core/interfaces/inner_api/osal/uhdf -I../../drivers/hdf_core/interfaces/inner_api/utils -I../../drivers/hdf_core/interfaces/inner_api/core -I../../drivers/hdf_core/interfaces/inner_api/ipc -I../../drivers/hdf_core/interfaces/inner_api/hdi -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -I../../base/startup/init/interfaces/innerkits/include -I../../base/startup/init/interfaces/innerkits/include/syspara -I../../base/startup/init/interfaces/innerkits/include/token -I../../base/startup/init/interfaces/innerkits/include/param  -Wno-implicit-function-declaration -Wno-unused-variable -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../device/soc/rockchip/rk3568/hardware/omx_il/osal/Rockchip_OSAL_Thread.c -o  obj/device/soc/rockchip/rk3568/hardware/omx_il/osal/RkOMX_OSAL/Rockchip_OSAL_Thread.o"}, {"file": "../../vendor/hihope/rk3568/bluetooth/src/bt_vendor_brcm.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/bt_vendor_brcm.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../vendor/hihope/rk3568/bluetooth/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include  -DUSE_CONTROLLER_BDADDR=TRUE -DFW_AUTO_DETECTION=TRUE -DBT_WAKE_VIA_PROC=FALSE -DSCO_PCM_ROUTING=0 -DSCO_PCM_IF_CLOCK_RATE=1 -DSCO_PCM_IF_FRAME_TYPE=0 -DSCO_PCM_IF_SYNC_MODE=0 -DSCO_PCM_IF_CLOCK_MODE=0 -DPCM_DATA_FMT_SHIFT_MODE=0 -DPCM_DATA_FMT_FILL_BITS=0x03 -DPCM_DATA_FMT_FILL_METHOD=0 -DPCM_DATA_FMT_FILL_NUM=0 -DPCM_DATA_FMT_JUSTIFY_MODE=0 -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -Wall -Werror -Wno-switch -Wno-unused-function -Wno-unused-parameter -Wno-unused-variable -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-unused-but-set-variable -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../vendor/hihope/rk3568/bluetooth/src/bt_vendor_brcm.c -o  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/bt_vendor_brcm.o"}, {"file": "../../vendor/hihope/rk3568/bluetooth/src/conf.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/conf.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../vendor/hihope/rk3568/bluetooth/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include  -DUSE_CONTROLLER_BDADDR=TRUE -DFW_AUTO_DETECTION=TRUE -DBT_WAKE_VIA_PROC=FALSE -DSCO_PCM_ROUTING=0 -DSCO_PCM_IF_CLOCK_RATE=1 -DSCO_PCM_IF_FRAME_TYPE=0 -DSCO_PCM_IF_SYNC_MODE=0 -DSCO_PCM_IF_CLOCK_MODE=0 -DPCM_DATA_FMT_SHIFT_MODE=0 -DPCM_DATA_FMT_FILL_BITS=0x03 -DPCM_DATA_FMT_FILL_METHOD=0 -DPCM_DATA_FMT_FILL_NUM=0 -DPCM_DATA_FMT_JUSTIFY_MODE=0 -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -Wall -Werror -Wno-switch -Wno-unused-function -Wno-unused-parameter -Wno-unused-variable -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-unused-but-set-variable -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../vendor/hihope/rk3568/bluetooth/src/conf.c -o  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/conf.o"}, {"file": "../../vendor/hihope/rk3568/bluetooth/src/hardware.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/hardware.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../vendor/hihope/rk3568/bluetooth/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include  -DUSE_CONTROLLER_BDADDR=TRUE -DFW_AUTO_DETECTION=TRUE -DBT_WAKE_VIA_PROC=FALSE -DSCO_PCM_ROUTING=0 -DSCO_PCM_IF_CLOCK_RATE=1 -DSCO_PCM_IF_FRAME_TYPE=0 -DSCO_PCM_IF_SYNC_MODE=0 -DSCO_PCM_IF_CLOCK_MODE=0 -DPCM_DATA_FMT_SHIFT_MODE=0 -DPCM_DATA_FMT_FILL_BITS=0x03 -DPCM_DATA_FMT_FILL_METHOD=0 -DPCM_DATA_FMT_FILL_NUM=0 -DPCM_DATA_FMT_JUSTIFY_MODE=0 -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -Wall -Werror -Wno-switch -Wno-unused-function -Wno-unused-parameter -Wno-unused-variable -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-unused-but-set-variable -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../vendor/hihope/rk3568/bluetooth/src/hardware.c -o  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/hardware.o"}, {"file": "../../vendor/hihope/rk3568/bluetooth/src/upio.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/upio.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../vendor/hihope/rk3568/bluetooth/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include  -DUSE_CONTROLLER_BDADDR=TRUE -DFW_AUTO_DETECTION=TRUE -DBT_WAKE_VIA_PROC=FALSE -DSCO_PCM_ROUTING=0 -DSCO_PCM_IF_CLOCK_RATE=1 -DSCO_PCM_IF_FRAME_TYPE=0 -DSCO_PCM_IF_SYNC_MODE=0 -DSCO_PCM_IF_CLOCK_MODE=0 -DPCM_DATA_FMT_SHIFT_MODE=0 -DPCM_DATA_FMT_FILL_BITS=0x03 -DPCM_DATA_FMT_FILL_METHOD=0 -DPCM_DATA_FMT_FILL_NUM=0 -DPCM_DATA_FMT_JUSTIFY_MODE=0 -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -Wall -Werror -Wno-switch -Wno-unused-function -Wno-unused-parameter -Wno-unused-variable -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-unused-but-set-variable -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../vendor/hihope/rk3568/bluetooth/src/upio.c -o  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/upio.o"}, {"file": "../../vendor/hihope/rk3568/bluetooth/src/userial_vendor.c", "directory": "/home/<USER>/openharmony-5.0.0/out/rk3568", "command": "/usr/bin/ccache ../../prebuilts/clang/ohos/linux-x86_64/llvm/bin/clang -MMD -MF  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/userial_vendor.o.d  -DV8_DEPRECATION_WARNINGS -D_GNU_SOURCE -DHAVE_SYS_UIO_H -D__MUSL__ -D_LIBCPP_HAS_MUSL_LIBC -D__BUILD_LINUX_WITH_CLANG -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -DCOMPONENT_BUILD -D__GNU_SOURCE=1 -DCHROMIUM_CXX_TWEAK_INLINES -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0  -I../../vendor/hihope/rk3568/bluetooth/include -I../../base/hiviewdfx/hilog/interfaces/native/innerkits/include -Iobj/third_party/musl/usr/include/arm-linux-ohos -Ioverride/third_party -I../.. -Igen -I../../commonlibrary/c_utils/base/include -I../../third_party/bounds_checking_function/include  -DUSE_CONTROLLER_BDADDR=TRUE -DFW_AUTO_DETECTION=TRUE -DBT_WAKE_VIA_PROC=FALSE -DSCO_PCM_ROUTING=0 -DSCO_PCM_IF_CLOCK_RATE=1 -DSCO_PCM_IF_FRAME_TYPE=0 -DSCO_PCM_IF_SYNC_MODE=0 -DSCO_PCM_IF_CLOCK_MODE=0 -DPCM_DATA_FMT_SHIFT_MODE=0 -DPCM_DATA_FMT_FILL_BITS=0x03 -DPCM_DATA_FMT_FILL_METHOD=0 -DPCM_DATA_FMT_FILL_NUM=0 -DPCM_DATA_FMT_JUSTIFY_MODE=0 -fno-strict-aliasing -Wno-builtin-macro-redefined -D__DATE__= -D__TIME__= -D__TIMESTAMP__= -funwind-tables -fPIC -fcolor-diagnostics -fmerge-all-constants -Xclang -mllvm -Xclang -instcombine-lower-dbg-declare=0 -no-canonical-prefixes -flto=thin -fsplit-lto-unit -ffunction-sections -fno-short-enums --target=arm-linux-ohos -march=armv7-a -mfloat-abi=softfp -mtune=generic-armv7-a -fstack-protector-strong -mfpu=neon -mthumb -Wall -Werror -Wextra -Wimplicit-fallthrough -Wthread-safety -Wno-missing-field-initializers -Wno-unused-parameter -Wno-c++11-narrowing -Wno-unneeded-internal-declaration -Wno-error=c99-designator -Wno-error=anon-enum-enum-conversion -Wno-error=sizeof-array-div -Wno-error=implicit-fallthrough -Wno-error=reorder-init-list -Wno-error=range-loop-construct -Wno-error=deprecated-copy -Wno-error=implicit-int-float-conversion -Wno-error=inconsistent-dllimport -Wno-error=unknown-warning-option -Wno-error=sign-compare -Wno-error=int-in-bool-context -Wno-error=return-stack-address -Wno-error=dangling-gsl -Wno-unused-but-set-variable -Wno-deprecated-declarations -Wno-unused-but-set-parameter -Wno-null-pointer-subtraction -Wno-unqualified-std-cast-call -Wno-user-defined-warnings -Wno-unused-lambda-capture -Wno-null-pointer-arithmetic -Wno-enum-compare-switch -O2 -fno-ident -fdata-sections -ffunction-sections -fomit-frame-pointer -gdwarf-3 -g2 -ggnu-pubnames -fno-common -Wheader-hygiene -Wstring-conversion -Wtautological-overlap-compare -Wall -Werror -Wno-switch -Wno-unused-function -Wno-unused-parameter -Wno-unused-variable -Wno-implicit-function-declaration -Wno-incompatible-pointer-types -Wno-unused-but-set-variable -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang  --sysroot=obj/third_party/musl -c ../../vendor/hihope/rk3568/bluetooth/src/userial_vendor.c -o  obj/vendor/hihope/rk3568/bluetooth/src/libbt_vendor/userial_vendor.o"}]